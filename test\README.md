# 测试文件夹

这个文件夹包含了项目的各种测试和调试文件。

## 📋 文件说明

### 🧪 系统测试
- `test_system_basic.py` - 基础系统测试
- `test_embedding_config.py` - 嵌入配置测试
- `test_vector_enhanced_system.py` - 向量增强系统测试

### 🔧 API测试
- `test_api_response.py` - API响应测试
- `test_server_debug.py` - 服务器调试测试
- `test_server_simple.py` - 简单服务器测试

### 📊 数据库测试
- `test_database_switch.py` - 数据库切换测试
- `test_database_switch_simple.py` - 简单数据库切换测试

### 🎯 功能测试
- `test_standardized_syntax.py` - 标准化语法测试
- `test_static_files.py` - 静态文件测试
- `test_think_explicit.py` - 思考过程测试

### 🔍 调试文件
- `debug_import.py` - 导入调试
- `debug_search.py` - 搜索调试
- `debug_frontend.html` - 前端调试

## 🚀 使用方法

运行测试文件：
```bash
cd test
python test_system_basic.py
```

或者从根目录运行：
```bash
python test/test_system_basic.py
```
