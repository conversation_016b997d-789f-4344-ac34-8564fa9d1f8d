#!/usr/bin/env python3
"""
动态参数示例
展示如何使用动态参数创建灵活的复杂提示词
"""

import sys
import os
import uuid
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager, ComplexPrompt

def create_dynamic_parameter_examples():
    """创建使用动态参数的示例提示词"""
    print("🔧 创建动态参数示例")
    print("=" * 60)
    
    try:
        manager = get_prompt_vectorstore_manager()
        
        # 示例1：您的需求（使用动态参数）
        prompt1 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="指标环比排名综合评分（动态版）",
            description="使用动态参数的指标评分分析，自动适配不同省份",
            trigger_questions=[
                "指标环比排名评分动态版",
                "动态省份综合评分分析",
                "自适应环比排名评分"
            ],
            processing_steps=[
                "[SQL查询:JT:{本省}当前电费情况如何] 获取{本省}的全国电费数据和排名",
                "[计算:综合评分逻辑] {本省}环比权重70%+排名权重30%，基准50分，劣化标准<50分，环比±1%对应±1分，排名±1名对应±5分，如无劣化则取排名最低指标",
                "[调用工具:知识:根据识别的{本省}劣化指标查询改进建议] 获取{本省}专属改进方案",
                "总结{本省}数据生成评分分析报告"
            ],
            response_format="""
# 📊 {本省}指标环比排名综合评分报告

## 📈 {本省}数据概览
[引用:查询结果]

## 🧮 {本省}评分详情
[引用:综合评分结果]

## ⚠️ {本省}劣化指标
[引用:劣化指标识别结果]

## 💡 {本省}改进建议
[引用:改进建议]

## 📋 评分说明
- 针对{本省}的专项分析
- 环比变动权重：70%
- 全国排名权重：30%
- 劣化标准：综合得分 < 50分
            """,
            priority=4,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例2：多省份对比（动态版）
        prompt2 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="省份电费对比分析（动态版）",
            description="使用动态参数进行省份间电费对比分析",
            trigger_questions=[
                "省份电费对比动态版",
                "动态省份电费分析",
                "{本省}电费与全国对比"
            ],
            processing_steps=[
                "[SQL查询:{数据库}:{本省}最近3个月电费数据] 获取{本省}本地电费数据",
                "[SQL查询:JT:全国各省电费平均水平] 获取全国对比数据",
                "[计算:{本省}电费差异分析] 计算{本省}电费与全国平均的差异百分比",
                "[调用工具:知识:{本省}电费优化建议] 获取{本省}专属优化方案",
                "生成{本省}电费对比分析报告"
            ],
            response_format="""
# 📊 {本省}电费对比分析报告

## 📈 {本省}电费数据
[引用:{本省}电费数据]

## 🌍 全国对比数据  
[引用:全国数据]

## 📊 {本省}差异分析
[引用:差异计算结果]

## 💡 {本省}优化建议
[引用:优化建议]

## 🎯 {本省}专项总结
基于{本省}实际情况的专业分析
            """,
            priority=3,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例3：用户问题自适应分析
        prompt3 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="用户问题自适应分析",
            description="根据用户问题和省份自动调整分析内容",
            trigger_questions=[
                "用户问题自适应分析",
                "动态问题分析",
                "智能问题响应"
            ],
            processing_steps=[
                "[SQL查询:{数据库}:关于{用户问题}的数据] 查询{本省}关于{用户问题}的相关数据",
                "[SQL查询:JT:全国{用户问题}统计] 获取全国关于{用户问题}的统计数据",
                "[计算:{本省}{用户问题}分析] 基于{用户问题}对{本省}数据进行专项分析",
                "[调用工具:知识:{本省}{用户问题}解决方案] 查询{本省}关于{用户问题}的解决方案",
                "生成{本省}关于{用户问题}的专项分析报告"
            ],
            response_format="""
# 📊 {本省}关于"{用户问题}"的分析报告

## 📋 问题概述
用户关心的问题：{用户问题}
分析省份：{本省}

## 📈 {本省}相关数据
[引用:{本省}数据]

## 🌍 全国对比
[引用:全国数据]

## 🧮 专项分析
[引用:分析结果]

## 💡 解决方案
[引用:解决方案]

## 🎯 总结
针对{本省}关于"{用户问题}"的专业建议
            """,
            priority=2,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例4：多参数综合示例
        prompt4 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="多参数综合分析示例",
            description="展示所有动态参数的综合使用",
            trigger_questions=[
                "多参数综合分析",
                "全参数动态示例",
                "参数替换演示"
            ],
            processing_steps=[
                "[SQL查询:{数据库}:{本省}关于{用户问题}的详细数据] 查询{省份代码}数据库中关于{用户问题}的信息",
                "[SQL查询:JT:全国关于{用户问题}的统计] 获取全国关于{用户问题}的对比数据",
                "[计算:{本省}综合分析] 对{本省}关于{用户问题}的数据进行综合计算分析",
                "[调用工具:知识:{本省}关于{用户问题}的专业建议] 获取{本省}针对{用户问题}的专业解决方案",
                "生成{本省}关于{用户问题}的完整分析报告"
            ],
            response_format="""
# 📊 {本省}关于"{用户问题}"的综合分析报告

## 🏛️ 基本信息
- 分析省份：{本省}
- 省份代码：{省份代码}
- 数据库：{数据库}
- 用户问题：{用户问题}

## 📈 {本省}数据分析
[引用:{本省}数据]

## 🌍 全国对比分析
[引用:全国数据]

## 🧮 {本省}专项计算
[引用:计算结果]

## 💡 {本省}专业建议
[引用:专业建议]

## 🎯 {本省}总结
基于{本省}实际情况，针对"{用户问题}"的专业分析和建议
            """,
            priority=1,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 添加示例提示词
        prompts = [prompt1, prompt2, prompt3, prompt4]
        success_count = 0
        
        for prompt in prompts:
            if manager.add_complex_prompt(prompt):
                success_count += 1
                print(f"✅ 成功添加示例提示词: {prompt.title}")
            else:
                print(f"❌ 添加示例提示词失败: {prompt.title}")
        
        print(f"\n📊 总结: 成功添加 {success_count}/{len(prompts)} 个示例提示词")
        
        if success_count > 0:
            print("\n🎯 动态参数说明:")
            print("📝 支持的动态参数:")
            print("  - {本省} → 省份中文名称 (如: 贵州、北京)")
            print("  - {省份代码} → 省份代码 (如: GZ、BJ)")
            print("  - {数据库} → 当前数据库名称")
            print("  - {用户问题} → 用户的原始问题")
            
            print("\n💡 使用效果:")
            print("  原始: [SQL查询:JT:{本省}当前电费情况如何]")
            print("  GZ用户: [SQL查询:JT:贵州当前电费情况如何]")
            print("  BJ用户: [SQL查询:JT:北京当前电费情况如何]")
            
            print("\n✅ 优势:")
            print("  - 一个提示词适配所有省份")
            print("  - 自动生成省份专属内容")
            print("  - 根据用户问题动态调整")
            print("  - 减少重复提示词创建")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 创建示例失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_parameter_usage_guide():
    """显示参数使用指南"""
    print("\n📋 动态参数使用指南")
    print("=" * 60)
    
    print("🎯 参数类型和用途:")
    
    print("\n1. 省份相关参数:")
    print("   {本省} - 省份中文名称，用于用户友好的显示")
    print("   {省份代码} - 省份代码，用于技术标识")
    print("   {数据库} - 数据库名称，通常等于省份代码")
    
    print("\n2. 用户相关参数:")
    print("   {用户问题} - 用户的原始问题，用于上下文相关分析")
    
    print("\n🔧 使用场景:")
    print("   📊 数据查询: [SQL查询:JT:{本省}当前电费情况如何]")
    print("   🧮 计算分析: [计算:{本省}与全国平均的差异]")
    print("   🔧 工具调用: [调用工具:知识:{本省}的{用户问题}建议]")
    print("   📝 文本描述: 生成{本省}关于{用户问题}的报告")
    
    print("\n💡 最佳实践:")
    print("   1. 在SQL查询中使用{本省}让查询更具体")
    print("   2. 在工具调用中使用参数让建议更精准")
    print("   3. 在回复格式中使用参数让报告更个性化")
    print("   4. 合理组合多个参数提高灵活性")

if __name__ == "__main__":
    print("🚀 动态参数示例")
    print("=" * 60)
    
    if create_dynamic_parameter_examples():
        show_parameter_usage_guide()
        
        print("\n" + "=" * 60)
        print("🎉 动态参数示例创建完成！")
        print("\n💡 现在您可以:")
        print("  1. 使用 [SQL查询:JT:{本省}当前电费情况如何]")
        print("  2. 一个提示词适配所有省份")
        print("  3. 自动生成个性化内容")
        print("  4. 根据用户问题动态调整")
        
        print("\n🚀 测试建议:")
        print("  - 不同省份用户测试同一个问题")
        print("  - 观察参数替换效果")
        print("  - 验证个性化内容生成")
    else:
        print("\n❌ 示例创建失败，请检查配置")
