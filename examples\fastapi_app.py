"""
FastAPI API 服务示例
提供RESTful API接口进行LangChain聊天
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uuid

from src.core import LLMFactory, VectorStoreManager
from src.chains import Chat<PERSON>hain, RAGChain
from src.utils import DocumentLoader
from src.config import settings

# 创建FastAPI应用
app = FastAPI(
    title="AI Chat LangChain API",
    description="基于LangChain的智能对话API服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量存储聊天会话
chat_sessions: Dict[str, ChatChain] = {}
rag_sessions: Dict[str, <PERSON><PERSON>hain] = {}

# Pydantic模型
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    model_config: Optional[Dict[str, Any]] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str

class RAGRequest(BaseModel):
    question: str
    session_id: Optional[str] = None
    model_config: Optional[Dict[str, Any]] = None

class RAGResponse(BaseModel):
    answer: str
    source_documents: List[Dict[str, Any]]
    session_id: str

class SessionInfo(BaseModel):
    session_id: str
    memory_info: Dict[str, Any]

class ErrorResponse(BaseModel):
    error: str
    message: str

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI Chat LangChain API",
        "version": "1.0.0",
        "endpoints": {
            "simple_chat": "/chat",
            "rag_chat": "/rag",
            "sessions": "/sessions",
            "docs": "/docs"
        }
    }

@app.post("/chat", response_model=ChatResponse)
async def simple_chat(request: ChatRequest):
    """简单聊天接口"""
    try:
        # 生成或使用现有会话ID
        session_id = request.session_id or str(uuid.uuid4())
        
        # 获取或创建聊天链
        if session_id not in chat_sessions:
            # 创建语言模型
            model_config = request.model_config or {}
            llm = LLMFactory.create_chat_model(
                provider=model_config.get("provider", "openai"),
                model_name=model_config.get("model_name", "gpt-3.5-turbo"),
                temperature=model_config.get("temperature", 0.7)
            )
            
            # 创建聊天链
            chat_chain = ChatChain(
                llm=llm,
                memory_type=model_config.get("memory_type", "buffer"),
                session_id=session_id,
                storage_type="memory"
            )
            
            chat_sessions[session_id] = chat_chain
        
        # 获取AI回复
        response = chat_sessions[session_id].chat(request.message)
        
        return ChatResponse(response=response, session_id=session_id)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/rag", response_model=RAGResponse)
async def rag_chat(request: RAGRequest):
    """RAG聊天接口"""
    try:
        # 生成或使用现有会话ID
        session_id = request.session_id or str(uuid.uuid4())
        
        # 获取或创建RAG链
        if session_id not in rag_sessions:
            # 创建语言模型和嵌入模型
            model_config = request.model_config or {}
            llm = LLMFactory.create_chat_model(
                provider=model_config.get("provider", "openai"),
                model_name=model_config.get("model_name", "gpt-3.5-turbo"),
                temperature=model_config.get("temperature", 0.7)
            )
            
            embeddings = LLMFactory.create_embeddings(provider="openai")
            
            # 创建示例文档和向量存储
            doc_loader = DocumentLoader()
            sample_docs = [
                doc_loader.create_document_from_text(
                    "LangChain是一个用于构建语言模型应用程序的强大框架。",
                    {"source": "langchain_doc", "type": "guide"}
                ),
                doc_loader.create_document_from_text(
                    "FastAPI是一个现代、快速的Web框架，用于构建Python API。",
                    {"source": "fastapi_doc", "type": "guide"}
                ),
                doc_loader.create_document_from_text(
                    "RAG（检索增强生成）结合了信息检索和文本生成技术。",
                    {"source": "rag_doc", "type": "guide"}
                )
            ]
            
            vectorstore = VectorStoreManager.create_vectorstore(
                store_type="chroma",
                embeddings=embeddings,
                documents=sample_docs
            )
            
            # 创建RAG链
            rag_chain = RAGChain(
                llm=llm,
                vectorstore=vectorstore,
                memory_type=model_config.get("memory_type", "buffer"),
                session_id=session_id,
                storage_type="memory"
            )
            
            rag_sessions[session_id] = rag_chain
        
        # 获取AI回复
        result = rag_sessions[session_id].chat(request.question)
        
        return RAGResponse(
            answer=result["answer"],
            source_documents=result["source_documents"],
            session_id=session_id
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions", response_model=List[SessionInfo])
async def get_sessions():
    """获取所有会话信息"""
    sessions = []
    
    # 简单聊天会话
    for session_id, chat_chain in chat_sessions.items():
        memory_info = chat_chain.get_memory_info()
        sessions.append(SessionInfo(
            session_id=session_id,
            memory_info=memory_info
        ))
    
    # RAG聊天会话
    for session_id, rag_chain in rag_sessions.items():
        memory_info = rag_chain.get_memory_info()
        sessions.append(SessionInfo(
            session_id=session_id,
            memory_info=memory_info
        ))
    
    return sessions

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """删除指定会话"""
    deleted = False
    
    if session_id in chat_sessions:
        del chat_sessions[session_id]
        deleted = True
    
    if session_id in rag_sessions:
        del rag_sessions[session_id]
        deleted = True
    
    if not deleted:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    return {"message": f"会话 {session_id} 已删除"}

@app.post("/sessions/{session_id}/clear")
async def clear_session_memory(session_id: str):
    """清空指定会话的记忆"""
    if session_id in chat_sessions:
        chat_sessions[session_id].clear_memory()
        return {"message": f"会话 {session_id} 的记忆已清空"}
    
    if session_id in rag_sessions:
        rag_sessions[session_id].clear_memory()
        return {"message": f"会话 {session_id} 的记忆已清空"}
    
    raise HTTPException(status_code=404, detail="会话不存在")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "chat_sessions": len(chat_sessions),
        "rag_sessions": len(rag_sessions)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 