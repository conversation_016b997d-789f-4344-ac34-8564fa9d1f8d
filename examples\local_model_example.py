"""
本地模型使用示例
演示如何配置和使用本地部署的LLM和BGE模型
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory, VectorStoreManager
from src.chains import ChatChain, RAGChain
from src.utils import DocumentLoader
from src.config import settings

def test_local_llm():
    """测试本地LLM连接"""
    print("=== 测试本地LLM连接 ===")
    
    if not settings.local_llm_enabled:
        print("❌ 本地LLM未启用，请在.env文件中设置 LOCAL_LLM_ENABLED=true")
        return False
    
    try:
        # 创建本地模型
        llm = LLMFactory.create_chat_model(
            provider="local",
            temperature=0.7
        )
        
        # 测试简单对话
        print("正在测试本地LLM...")
        response = llm.invoke("你好，请简单介绍一下自己")
        print(f"✅ 本地LLM响应: {response}")
        return True
        
    except Exception as e:
        print(f"❌ 本地LLM测试失败: {e}")
        print("请检查:")
        print("- 本地LLM服务是否正在运行")
        print(f"- API地址是否正确: {settings.local_llm_model_url}")
        print("- API密钥是否正确")
        print("- 网络连接是否正常")
        return False

def test_local_bge():
    """测试本地BGE嵌入模型连接"""
    print("\n=== 测试本地BGE嵌入模型连接 ===")
    
    if not settings.local_bge_enabled:
        print("❌ 本地BGE未启用，请在.env文件中设置 LOCAL_BGE_ENABLED=true")
        return False
    
    try:
        # 创建本地BGE嵌入模型
        embeddings = LLMFactory.create_embeddings(provider="local_bge")
        
        # 测试嵌入功能
        print("正在测试本地BGE嵌入模型...")
        test_texts = ["测试文本", "这是第二个测试文本"]
        vectors = embeddings.embed_documents(test_texts)
        
        print(f"✅ 本地BGE响应:")
        print(f"   文本数量: {len(test_texts)}")
        print(f"   向量数量: {len(vectors)}")
        if vectors and len(vectors) > 0:
            print(f"   向量维度: {len(vectors[0])}")
        return True
        
    except Exception as e:
        print(f"❌ 本地BGE测试失败: {e}")
        print("请检查:")
        print("- 本地BGE服务是否正在运行")
        print(f"- API地址是否正确: {settings.local_bge_api_url}")
        print("- API密钥是否正确")
        print("- 网络连接是否正常")
        return False

def run_local_chat():
    """运行本地聊天示例"""
    print("\n=== 本地模型聊天示例 ===")
    
    try:
        # 创建本地模型
        llm = LLMFactory.create_chat_model(provider="local")
        
        # 创建聊天链
        chat_chain = ChatChain(
            llm=llm,
            memory_type="buffer",
            session_id="local_chat",
            storage_type="memory"
        )
        
        print("✅ 本地聊天系统已启动!")
        print("💡 输入 'quit' 退出")
        print("-" * 30)
        
        while True:
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
            elif not user_input:
                continue
            
            try:
                response = chat_chain.chat(user_input)
                print(f"🤖 AI: {response}")
            except Exception as e:
                print(f"❌ 对话出错: {e}")
                
    except Exception as e:
        print(f"❌ 初始化本地聊天失败: {e}")

def run_local_rag():
    """运行本地RAG示例"""
    print("\n=== 本地模型RAG示例 ===")
    
    try:
        # 创建本地模型
        llm = LLMFactory.create_chat_model(provider="local")
        embeddings = LLMFactory.create_embeddings(provider="local_bge")
        
        # 创建示例文档
        doc_loader = DocumentLoader()
        documents = [
            doc_loader.create_document_from_text(
                "本地大语言模型是部署在本地服务器上的AI模型，具有数据隐私保护和响应速度快的优势。",
                {"source": "local_llm_intro"}
            ),
            doc_loader.create_document_from_text(
                "BGE（BAAI General Embedding）是智源研究院开发的中文嵌入模型，支持多种下游任务。",
                {"source": "bge_intro"}
            ),
            doc_loader.create_document_from_text(
                "本地部署的优势包括：数据不出网、响应延迟低、可定制化程度高、成本可控。",
                {"source": "local_deployment_benefits"}
            )
        ]
        
        # 创建向量存储
        vectorstore = VectorStoreManager.create_vectorstore(
            store_type="chroma",
            embeddings=embeddings,
            documents=documents,
            persist_directory="./data/local_rag_db"
        )
        
        # 创建RAG链
        rag_chain = RAGChain(
            llm=llm,
            vectorstore=vectorstore,
            memory_type="buffer",
            session_id="local_rag",
            storage_type="memory"
        )
        
        print("✅ 本地RAG系统已启动!")
        print("💡 输入 'quit' 退出")
        print("💡 尝试问一些关于本地模型的问题")
        print("-" * 40)
        
        while True:
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
            elif not user_input:
                continue
            
            try:
                result = rag_chain.chat(user_input)
                if isinstance(result, dict):
                    print(f"🤖 AI: {result.get('answer', '抱歉，无法回答这个问题。')}")
                    
                    # 显示参考文档
                    source_docs = result.get("source_documents", [])
                    if source_docs:
                        print(f"\n📖 参考文档:")
                        for i, doc in enumerate(source_docs[:2]):
                            print(f"  {i+1}. {doc.page_content[:100]}...")
                else:
                    print(f"🤖 AI: {result}")
                    
            except Exception as e:
                print(f"❌ 对话出错: {e}")
                
    except Exception as e:
        print(f"❌ 初始化本地RAG失败: {e}")

def show_config():
    """显示当前配置"""
    print("=== 当前本地模型配置 ===")
    print(f"本地LLM启用: {settings.local_llm_enabled}")
    if settings.local_llm_enabled:
        print(f"  模型URL: {settings.local_llm_model_url}")
        print(f"  模型名称: {settings.local_llm_model_name}")
        print(f"  温度: {settings.local_llm_temperature}")
        print(f"  超时: {settings.local_llm_timeout}秒")
    
    print(f"\n本地BGE启用: {settings.local_bge_enabled}")
    if settings.local_bge_enabled:
        print(f"  API URL: {settings.local_bge_api_url}")
        print(f"  模型名称: {settings.local_bge_model_name}")
        print(f"  超时: {settings.local_bge_timeout}秒")
        if settings.local_bge_fallback_url:
            print(f"  备用URL: {settings.local_bge_fallback_url}")

def main():
    """主函数"""
    print("=== 本地模型配置和测试工具 ===\n")
    
    show_config()
    
    if not settings.local_llm_enabled and not settings.local_bge_enabled:
        print("\n❌ 未启用任何本地模型服务")
        print("请在.env文件中配置本地模型参数:")
        print("LOCAL_LLM_ENABLED=true")
        print("LOCAL_LLM_API_KEY=your_api_key")
        print("LOCAL_LLM_MODEL_URL=http://localhost:9997/v1/chat/completions")
        print("LOCAL_LLM_MODEL_NAME=your_model_name")
        print("LOCAL_BGE_ENABLED=true")
        print("LOCAL_BGE_API_URL=http://localhost:9997/v1/embeddings")
        return
    
    while True:
        print("\n请选择要执行的操作:")
        print("1. 测试本地LLM连接")
        print("2. 测试本地BGE连接")
        print("3. 运行本地聊天示例")
        print("4. 运行本地RAG示例")
        print("5. 显示配置信息")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 再见!")
            break
        elif choice == "1":
            test_local_llm()
        elif choice == "2":
            test_local_bge()
        elif choice == "3":
            run_local_chat()
        elif choice == "4":
            run_local_rag()
        elif choice == "5":
            show_config()
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main() 