"""
RAG (检索增强生成) 聊天应用示例
演示如何使用RAGChain进行基于文档的对话
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory, VectorStoreManager
from src.chains import RAGChain
from src.utils import DocumentLoader
from src.config import settings


def main():
    """主函数"""
    print("=== AI Chat LangChain RAG聊天示例 ===\n")
    
    try:
        # 模型选择
        print("请选择要使用的大语言模型:")
        print("1. OpenAI (需要API密钥)")
        print("2. Anthropic Claude (需要API密钥)")
        print("3. 本地模型 (需要配置本地服务)")
        
        llm_choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if llm_choice == "1":
            llm_provider = "openai"
            llm_model = input("请输入模型名称 (默认: gpt-3.5-turbo): ").strip() or "gpt-3.5-turbo"
        elif llm_choice == "2":
            llm_provider = "anthropic"
            llm_model = input("请输入模型名称 (默认: claude-3-haiku-20240307): ").strip() or "claude-3-haiku-20240307"
        elif llm_choice == "3":
            llm_provider = "local"
            llm_model = None
            print("使用本地模型，请确保已在.env文件中配置相关参数")
        else:
            print("无效选择，使用默认OpenAI模型")
            llm_provider = "openai"
            llm_model = "gpt-3.5-turbo"
        
        # Embedding模型选择
        print("\n请选择要使用的嵌入模型:")
        print("1. OpenAI Embeddings (需要API密钥)")
        print("2. 本地BGE模型 (需要配置本地BGE服务)")
        
        emb_choice = input("\n请输入选择 (1/2): ").strip()
        
        if emb_choice == "2":
            emb_provider = "local_bge"
            print("使用本地BGE模型，请确保已在.env文件中配置相关参数")
        else:
            emb_provider = "openai"
            if emb_choice != "1":
                print("无效选择，使用默认OpenAI嵌入模型")
        
        # 创建语言模型
        print(f"\n正在初始化{llm_provider}语言模型...")
        llm = LLMFactory.create_chat_model(
            provider=llm_provider,
            model_name=llm_model,
            temperature=0.7
        )
        print("✅ 语言模型初始化成功!")
        
        # 创建嵌入模型
        print(f"\n正在初始化{emb_provider}嵌入模型...")
        embeddings = LLMFactory.create_embeddings(provider=emb_provider)
        print("✅ 嵌入模型初始化成功!")
        
        # 检查或加载文档
        print("\n正在准备文档数据...")
        doc_loader = DocumentLoader()
        
        # 检查是否有用户文档
        doc_directory = "./data/documents"
        if os.path.exists(doc_directory) and os.listdir(doc_directory):
            print(f"发现文档目录: {doc_directory}")
            choice = input("是否加载该目录中的文档? (y/n): ").strip().lower()
            if choice == 'y':
                documents = doc_loader.load_directory(doc_directory)
                print(f"✅ 成功加载 {len(documents)} 个文档")
            else:
                documents = create_sample_documents(doc_loader)
        else:
            print("未发现文档目录或目录为空，使用示例文档")
            documents = create_sample_documents(doc_loader)
        
        # 创建向量存储
        print("\n正在创建向量存储...")
        vectorstore = VectorStoreManager.create_vectorstore(
            store_type="chroma",
            embeddings=embeddings,
            documents=documents,
            persist_directory="./data/chroma_rag_db"
        )
        print("✅ 向量存储创建成功!")
        
        # 创建RAG链
        rag_chain = RAGChain(
            llm=llm,
            vectorstore=vectorstore,
            memory_type="buffer",
            session_id="rag_chat_session",
            storage_type="memory"
        )
        
        print("\n✅ RAG聊天系统已启动!")
        print("💡 输入 'quit'、'exit' 或 'q' 退出程序")
        print("💡 输入 'clear' 清空聊天记忆")
        print("💡 输入 'info' 查看记忆信息")
        print("💡 输入 'docs' 查看已加载的文档信息")
        print("-" * 50)
        
        # 开始RAG聊天循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 您: ").strip()
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                elif user_input.lower() == 'clear':
                    rag_chain.clear_memory()
                    print("🗑️ 聊天记忆已清空")
                    continue
                elif user_input.lower() == 'info':
                    memory_info = rag_chain.get_memory_info()
                    print(f"📊 记忆信息: {memory_info}")
                    continue
                elif user_input.lower() == 'docs':
                    print(f"📚 已加载文档数量: {len(documents)}")
                    for i, doc in enumerate(documents[:5]):  # 显示前5个文档
                        content_preview = doc.page_content[:100] + "..." if len(doc.page_content) > 100 else doc.page_content
                        print(f"  {i+1}. {content_preview}")
                        if doc.metadata:
                            print(f"     元数据: {doc.metadata}")
                    if len(documents) > 5:
                        print(f"  ... 还有 {len(documents) - 5} 个文档")
                    continue
                elif not user_input:
                    print("⚠️ 请输入有效内容")
                    continue
                
                # 获取RAG回复
                print("🤖 AI: ", end="", flush=True)
                result = rag_chain.chat(user_input)
                
                if isinstance(result, dict):
                    print(result.get("answer", "抱歉，我无法回答这个问题。"))
                    
                    # 显示相关文档
                    source_docs = result.get("source_documents", [])
                    if source_docs and len(source_docs) > 0:
                        print(f"\n📖 参考文档 ({len(source_docs)} 个):")
                        for i, doc in enumerate(source_docs[:2]):  # 只显示前2个最相关的文档
                            content_preview = doc.page_content[:150] + "..." if len(doc.page_content) > 150 else doc.page_content
                            print(f"  {i+1}. {content_preview}")
                            if hasattr(doc, 'metadata') and doc.metadata:
                                print(f"     来源: {doc.metadata}")
                else:
                    print(result)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("请检查网络连接和API配置")
                continue
                
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        print("\n💡 解决建议:")
        if "OpenAI" in str(e):
            print("- 检查 OPENAI_API_KEY 环境变量是否设置")
            print("- 检查网络连接是否正常")
        elif "Anthropic" in str(e):
            print("- 检查 ANTHROPIC_API_KEY 环境变量是否设置")
            print("- 检查网络连接是否正常")
        elif "本地" in str(e) or "local" in str(e).lower() or "BGE" in str(e):
            print("- 检查本地模型服务是否运行")
            print("- 检查本地模型相关环境变量配置")
            print("- 检查本地BGE服务相关环境变量配置")
            print("- 检查网络连接和服务可用性")
        print("- 检查 .env 文件配置")
        print("- 检查依赖包是否正确安装")


if __name__ == "__main__":
    main() 