"""
测试您的Qwen3_30B_A3B模型
简单验证模型配置和连接
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory
from src.chains import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.config import settings

def test_model_config():
    """测试模型配置"""
    print("=== 测试您的Qwen3_30B_A3B模型配置 ===\n")
    
    print("配置信息:")
    print(f"  模型URL: {settings.local_llm_model_url}")
    print(f"  模型名称: {settings.local_llm_model_name}")
    print(f"  API密钥: {settings.local_llm_api_key[:20]}..." if settings.local_llm_api_key else "  API密钥: 未设置")
    print(f"  温度: {settings.local_llm_temperature}")
    print()

def test_model_connection():
    """测试模型连接"""
    print("正在测试模型连接...")
    
    try:
        # 创建本地模型
        llm = LLMFactory.create_chat_model(
            provider="local",
            temperature=0.7
        )
        print("✅ 模型创建成功")
        
        # 测试简单调用
        print("正在测试模型响应...")
        response = llm.invoke("你好，请简单介绍一下自己")
        print(f"✅ 模型响应: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def run_simple_chat():
    """运行简单聊天"""
    print("\n=== 开始聊天测试 ===")
    
    try:
        # 创建聊天链
        llm = LLMFactory.create_chat_model(provider="local")
        chat_chain = ChatChain(
            llm=llm,
            memory_type="buffer",
            session_id="test_session",
            storage_type="memory"
        )
        
        print("聊天系统已启动! 输入 'quit' 退出")
        print("-" * 40)
        
        while True:
            user_input = input("\n您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("再见!")
                break
            elif not user_input:
                continue
            
            try:
                response = chat_chain.chat(user_input)
                print(f"AI: {response}")
            except Exception as e:
                print(f"错误: {e}")
                
    except Exception as e:
        print(f"聊天系统初始化失败: {e}")

def main():
    """主函数"""
    print("这个工具将测试您的Qwen3_30B_A3B模型配置\n")
    
    # 检查配置文件
    env_file = ".env"
    template_file = "env.template"
    
    if not os.path.exists(env_file):
        print("❌ 未找到.env配置文件")
        if os.path.exists(template_file):
            print(f"请复制模板文件创建配置:")
            print(f"  Windows: copy {template_file} .env")
            print(f"  Linux/Mac: cp {template_file} .env")
        else:
            print("请创建.env文件并配置您的模型参数")
        return
    else:
        print("✅ 找到.env配置文件")
    
    # 检查配置
    if not settings.local_llm_enabled:
        print("❌ 本地LLM未启用")
        print("请在.env文件中设置: LOCAL_LLM_ENABLED=true")
        return
    
    if not settings.local_llm_model_url:
        print("❌ 未设置模型URL")
        print("请在.env文件中设置您的模型URL")
        return
    
    # 显示配置
    test_model_config()
    
    # 测试连接
    if test_model_connection():
        print("\n模型测试成功! 您可以:")
        print("1. 运行 python examples/simple_chat.py 进行聊天")
        print("2. 运行 python examples/rag_chat.py 进行RAG对话")
        print("3. 或者在这里继续测试聊天")
        
        choice = input("\n是否在这里测试聊天? (y/n): ").strip().lower()
        if choice == 'y':
            run_simple_chat()
    else:
        print("\n模型测试失败，请检查:")
        print("1. 模型服务是否正在运行")
        print("2. URL和API密钥是否正确")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main() 