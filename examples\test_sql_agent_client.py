"""
SQL Agent API 客户端测试脚本
用于测试SQL Agent服务器的各种功能
"""

import sys
import os
import requests
import json
import time
from typing import Dict, Any

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class SqlAgentClient:
    """SQL Agent API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            return {
                "success": response.status_code == 200,
                "data": response.json() if response.status_code == 200 else None,
                "error": None if response.status_code == 200 else f"状态码: {response.status_code}"
            }
        except Exception as e:
            return {"success": False, "data": None, "error": str(e)}
    
    def sql_query(self, question: str, session_id: str = None, 
                  include_sql: bool = True, include_data: bool = True) -> Dict[str, Any]:
        """SQL查询"""
        try:
            payload = {
                "question": question,
                "include_sql": include_sql,
                "include_data": include_data
            }
            if session_id:
                payload["session_id"] = session_id
            
            response = self.session.post(
                f"{self.base_url}/v1/sql/query",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            return {
                "success": response.status_code == 200,
                "data": response.json() if response.status_code == 200 else None,
                "error": None if response.status_code == 200 else f"状态码: {response.status_code}, 响应: {response.text}"
            }
        except Exception as e:
            return {"success": False, "data": None, "error": str(e)}
    
    def chat_completion(self, question: str, model: str = "sql-agent") -> Dict[str, Any]:
        """OpenAI兼容的聊天完成"""
        try:
            payload = {
                "model": model,
                "messages": [
                    {"role": "user", "content": question}
                ]
            }
            
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            return {
                "success": response.status_code == 200,
                "data": response.json() if response.status_code == 200 else None,
                "error": None if response.status_code == 200 else f"状态码: {response.status_code}, 响应: {response.text}"
            }
        except Exception as e:
            return {"success": False, "data": None, "error": str(e)}
    
    def list_sessions(self) -> Dict[str, Any]:
        """列出活跃会话"""
        try:
            response = self.session.get(f"{self.base_url}/v1/sessions")
            return {
                "success": response.status_code == 200,
                "data": response.json() if response.status_code == 200 else None,
                "error": None if response.status_code == 200 else f"状态码: {response.status_code}"
            }
        except Exception as e:
            return {"success": False, "data": None, "error": str(e)}


def print_result(title: str, result: Dict[str, Any]):
    """打印测试结果"""
    print(f"\n{'='*20} {title} {'='*20}")
    
    if result["success"]:
        print("✅ 成功")
        if result["data"]:
            print("📊 响应数据:")
            print(json.dumps(result["data"], indent=2, ensure_ascii=False))
    else:
        print("❌ 失败")
        print(f"错误: {result['error']}")
    
    print("=" * (42 + len(title)))


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试SQL Agent服务器基本功能")
    
    client = SqlAgentClient()
    
    # 1. 健康检查
    print("\n1️⃣ 健康检查...")
    health_result = client.health_check()
    print_result("健康检查", health_result)
    
    if not health_result["success"]:
        print("❌ 服务器不可用，请先启动服务器")
        return
    
    # 2. 测试SQL查询端点
    print("\n2️⃣ 测试SQL查询端点...")
    
    test_questions = [
        "2025年4月电费是多少",
        "最近一个月的用电量",
        "2025年3月和4月的电费对比"
    ]
    
    session_id = f"test-session-{int(time.time())}"
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n📝 测试问题 {i}: {question}")
        sql_result = client.sql_query(question, session_id=session_id)
        print_result(f"SQL查询-{i}", sql_result)
        
        if sql_result["success"]:
            time.sleep(1)  # 避免过快请求
    
    # 3. 测试OpenAI兼容端点
    print("\n3️⃣ 测试OpenAI兼容端点...")
    chat_result = client.chat_completion("查询所有用户的电费总和")
    print_result("聊天完成", chat_result)
    
    # 4. 会话管理
    print("\n4️⃣ 测试会话管理...")
    sessions_result = client.list_sessions()
    print_result("活跃会话", sessions_result)


def test_dify_compatibility():
    """测试Dify兼容性"""
    print("\n🔗 测试Dify平台兼容性")
    
    client = SqlAgentClient()
    
    # 模拟Dify发送的请求格式
    dify_request = {
        "model": "sql-agent",
        "messages": [
            {"role": "system", "content": "你是一个专业的数据库查询助手"},
            {"role": "user", "content": "帮我查询2025年4月的电费账单"}
        ],
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    print("📤 发送Dify格式的请求...")
    response = client.session.post(
        f"{client.base_url}/v1/chat/completions",
        json=dify_request,
        headers={"Content-Type": "application/json"}
    )
    
    result = {
        "success": response.status_code == 200,
        "data": response.json() if response.status_code == 200 else None,
        "error": None if response.status_code == 200 else f"状态码: {response.status_code}"
    }
    
    print_result("Dify兼容性测试", result)


def test_error_handling():
    """测试错误处理"""
    print("\n🚨 测试错误处理机制")
    
    client = SqlAgentClient()
    
    # 测试无效请求
    print("\n📝 测试空问题...")
    empty_result = client.sql_query("")
    print_result("空问题测试", empty_result)
    
    # 测试非SQL相关问题
    print("\n📝 测试非SQL问题...")
    non_sql_result = client.sql_query("今天天气怎么样？")
    print_result("非SQL问题测试", non_sql_result)
    
    # 测试无效的API端点
    print("\n📝 测试无效端点...")
    try:
        response = client.session.get(f"{client.base_url}/invalid/endpoint")
        invalid_result = {
            "success": response.status_code == 200,
            "data": response.json() if response.status_code != 404 else None,
            "error": f"状态码: {response.status_code}" if response.status_code != 404 else None
        }
    except Exception as e:
        invalid_result = {"success": False, "data": None, "error": str(e)}
    
    print_result("无效端点测试", invalid_result)


def interactive_test():
    """交互式测试"""
    print("\n🎮 进入交互式测试模式")
    print("输入问题测试SQL Agent，输入 'quit' 退出")
    
    client = SqlAgentClient()
    session_id = f"interactive-{int(time.time())}"
    
    while True:
        try:
            question = input("\n❓ 请输入您的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出', 'q']:
                print("👋 退出交互式测试")
                break
            
            if not question:
                print("⚠️ 请输入有效问题")
                continue
            
            print("🔄 处理中...")
            start_time = time.time()
            
            result = client.sql_query(question, session_id=session_id)
            
            end_time = time.time()
            
            if result["success"]:
                data = result["data"]
                print(f"\n✅ 查询成功 (耗时: {(end_time - start_time):.2f}秒)")
                print(f"🤖 回答: {data.get('answer', '无回答')}")
                
                if data.get('generated_sql'):
                    print(f"📝 生成的SQL: {data['generated_sql']}")
                
                if data.get('row_count') is not None:
                    print(f"📊 返回行数: {data['row_count']}")
                
                if data.get('data') and len(data['data']) > 0:
                    print("🗃️ 查询结果:")
                    for i, row in enumerate(data['data'][:3], 1):  # 只显示前3行
                        print(f"   {i}. {dict(row)}")
                    if len(data['data']) > 3:
                        print(f"   ... 还有 {len(data['data']) - 3} 行数据")
            else:
                print(f"\n❌ 查询失败: {result['error']}")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出测试")
            break
        except Exception as e:
            print(f"\n💥 发生错误: {e}")


def main():
    """主函数"""
    print("🧪 SQL Agent API 服务器测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试模式：")
        print("1. 基本功能测试")
        print("2. Dify兼容性测试")
        print("3. 错误处理测试")
        print("4. 交互式测试")
        print("5. 全面测试 (1+2+3)")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 退出测试工具")
            break
        elif choice == "1":
            test_basic_functionality()
        elif choice == "2":
            test_dify_compatibility()
        elif choice == "3":
            test_error_handling()
        elif choice == "4":
            interactive_test()
        elif choice == "5":
            test_basic_functionality()
            test_dify_compatibility()
            test_error_handling()
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main() 