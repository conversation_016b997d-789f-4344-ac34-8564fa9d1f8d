"""
费用分析模块鉴权配置
"""
import os
import base64
from typing import Set, Dict, Tuple, Optional

# API 鉴权配置
class AuthConfig:
    """鉴权配置"""
    
    # Token前缀到省份代码的映射（固定部分）
    TOKEN_PREFIX_MAPPING: Dict[str, str] = {
        "nx_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1": "NX",   # 宁夏
        "gs_9a5d6e3f8c2b7a4e1d9f6c3b8e5a2d7f4c1b9e6": "GS",   # 甘肃
        "he_7c2f5b8e1a4d9c6f3b8e5a2d7f4c1b9e6a3d8f5": "HE",   # 河北
        "gz_6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4": "GZ",   # 贵州
        "xxl_5a0d3c6f9b2e7a4d1c8f5b2e9a6d3c0f7b4e1a9": "XXL", # 特殊区域
        "jt_4f9c2b5e8a1d7c0f3b6e9a2d5c8f1b4e7a0d3c6": "JT",   # 总部
        "bj_3e8b1c4f7a0d9c2f5b8e1a4d7c0f3b6e9a2d5c8": "BJ",   # 北京
        "tj_2d7a0c3f6b9e2a5d8c1f4b7e0a3d6c9f2b5e8a1": "TJ",   # 天津
        "sx_1c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7c0": "SX",   # 山西
        "nm_0b5e8a1d4c7f0b3e6a9d2c5f8b1e4a7d0c3f6b9": "NM",   # 内蒙古
        "ln_f9a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6f9b2e5a8": "LN",  # 辽宁
        "jl_e8b3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7": "JL",  # 吉林
        "hl_d7c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6": "HL",  # 黑龙江
        "sh_c6f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5": "SH",  # 上海
        "js_b5e0a3d6c9f2b5e8a1d4c7f0b3e6a9d2c5f8b1e4": "JS",  # 江苏
        "zj_a4d9c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3": "ZJ",  # 浙江
        "ah_98c8f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2": "AH",  # 安徽
        "fj_87b7e0a3d6c9f2b5e8a1d4c7f0b3e6a9d2c5f8b1": "FJ",  # 福建
        "jx_76a6d9c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0": "JX",  # 江西
        "sd_65f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6f9": "SD",  # 山东
        "ha_54e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8": "HA",  # 河南
        "hb_43d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7": "HB",  # 湖北
        "hn_32c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6": "HN",  # 湖南
        "gd_21b1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5": "GD",  # 广东
        "gx_10a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4": "GX",  # 广西
        "hi_0f9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3": "HI",  # 海南
        "cq_fe8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2": "CQ",  # 重庆
        "sc_ed7da0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1": "SC",  # 四川
        "yn_dc6c9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0": "YN",  # 云南
        "xz_cb5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9": "XZ",  # 西藏
        "sn_ba4a7da0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8": "SN",  # 陕西
        "qh_a939c6c9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7": "QH",  # 青海
        "xj_9828b5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6": "XJ",  # 新疆
    }

    @classmethod
    def _parse_province_database_mapping(cls) -> Dict[str, str]:
        """
        从环境变量解析省份数据库映射关系

        Returns:
            Dict[str, str]: 省份代码到数据库名称的映射
        """
        mapping = {}
        env_mapping = os.getenv("PROVINCE_DATABASE_MAPPING", "")

        if env_mapping:
            # 解析格式：省份代码:数据库名称:省份名称,省份代码:数据库名称:省份名称,...
            for item in env_mapping.split(","):
                if ":" in item:
                    parts = item.strip().split(":")
                    if len(parts) >= 2:
                        province_code = parts[0].strip()
                        database_name = parts[1].strip()
                        mapping[province_code] = database_name

        return mapping

    @classmethod
    def get_token_database_mapping(cls) -> Dict[str, str]:
        """
        动态生成Token与数据库的映射关系

        Returns:
            Dict[str, str]: Token到数据库名称的映射
        """
        # 获取省份数据库映射
        province_db_mapping = cls._parse_province_database_mapping()

        # 生成Token到数据库的映射
        token_db_mapping = {}
        for token, province_code in cls.TOKEN_PREFIX_MAPPING.items():
            database_name = province_db_mapping.get(province_code)
            if database_name:
                token_db_mapping[token] = database_name

        return token_db_mapping

    @classmethod
    def get_valid_tokens(cls) -> Set[str]:
        """获取所有有效token"""
        # 从动态映射中获取tokens
        token_db_mapping = cls.get_token_database_mapping()
        tokens = set(token_db_mapping.keys())

        # 从环境变量添加token（格式：token:database）
        env_token_db = os.getenv("EXPENSE_API_TOKEN_DB")
        if env_token_db and ":" in env_token_db:
            token, database = env_token_db.split(":", 1)
            tokens.add(token)

        return tokens
    
    @classmethod
    def get_database_by_token(cls, token: str) -> Optional[str]:
        """根据token获取对应的数据库名称"""
        # 先检查动态映射表
        token_db_mapping = cls.get_token_database_mapping()
        database = token_db_mapping.get(token)
        if database:
            return database

        # 检查环境变量
        env_token_db = os.getenv("EXPENSE_API_TOKEN_DB")
        if env_token_db and ":" in env_token_db:
            env_token, env_database = env_token_db.split(":", 1)
            if env_token == token:
                return env_database

        return None

    @classmethod
    def is_valid_token(cls, token: str) -> bool:
        """检查token是否有效"""
        return token in cls.get_valid_tokens()
    
    # 是否启用鉴权 (可以通过环境变量关闭，方便开发调试)
    ENABLE_AUTH: bool = os.getenv("EXPENSE_API_AUTH_DISABLED", "false").lower() != "true"


# 使用示例:
# 1. 使用预设token访问对应省份数据库:
#    - Authorization: Bearer gz_6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4  (访问贵州数据库)
#    - Authorization: Bearer bj_3e8b1c4f7a0d9c2f5b8e1a4d7c0f3b6e9a2d5c8  (访问北京数据库)
#    - Authorization: Bearer jt_4f9c2b5e8a1d7c0f3b6e9a2d5c8f1b4e7a0d3c6  (访问总部数据库)
# 2. 数据库名称从环境变量PROVINCE_DATABASE_MAPPING动态读取
#    - 格式: 省份代码:数据库名称:省份名称,省份代码:数据库名称:省份名称,...
#    - 示例: XJ:analysis_xjj:新疆  (修改新疆数据库名为analysis_xjj)
# 3. 设置环境变量: export EXPENSE_API_TOKEN_DB=your_token:your_database
# 4. 开发时关闭鉴权: export EXPENSE_API_AUTH_DISABLED=true
# 5. Token自动决定访问哪个数据库，用户无法直接修改
# 6. 支持全国32个省份+总部+特殊区域，共34个数据库，数据库名称可动态配置
