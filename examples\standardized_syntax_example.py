#!/usr/bin/env python3
"""
标准化语法示例
展示新的标准化命令语法的使用方法
"""

import sys
import os
import uuid
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager, ComplexPrompt

def create_standardized_syntax_examples():
    """创建标准化语法示例提示词"""
    print("🔧 创建标准化语法示例")
    print("=" * 60)
    
    try:
        manager = get_prompt_vectorstore_manager()
        
        # 示例1：电费情况综合分析（使用标准化语法）
        prompt1 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="电费情况综合分析（标准化语法版）",
            description="使用标准化语法进行电费综合分析，包括数据查询、计算、工具调用等",
            trigger_questions=[
                "电费情况综合分析标准版",
                "使用标准语法分析电费",
                "标准化电费分析流程"
            ],
            processing_steps=[
                "[SQL查询:原始:当前电费情况如何] 获取本省电费基础数据",
                "[SQL查询:JT:全国电费平均水平] 获取全国对比数据",
                "[变量:本省数据=步骤1查询结果] 保存本省电费数据",
                "[变量:全国数据=步骤2查询结果] 保存全国电费数据",
                "[计算:本省电费与全国平均的差异百分比] 计算差异指标",
                "[调用工具:knowledge_search:电费优化建议] 获取改进建议",
                "生成综合分析报告"
            ],
            response_format="""
# 📊 电费情况综合分析报告（标准化版）

## 📈 本省电费数据
[引用:本省数据]

## 🌍 全国对比数据  
[引用:全国数据]

## 📊 差异分析
[引用:计算结果]

## 💡 改进建议
[引用:工具调用结果]
            """,
            priority=4,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例2：复杂评分分析（您的实际需求）
        prompt2 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="指标环比与排名综合评分分析",
            description="根据环比变动和全国排名进行综合评分，自动识别劣化指标并提供改进建议",
            trigger_questions=[
                "指标环比排名综合评分",
                "环比变动排名分析",
                "指标劣化评分分析",
                "综合评分改进建议"
            ],
            processing_steps=[
                "[SQL查询:JT:当前电费情况如何] 获取全国电费数据和排名",
                "[变量:查询结果=步骤1结果] 保存查询数据",
                """[计算:综合评分逻辑] 根据查询结果中的指标环比变动情况与全国排名综合赋值并打分：
                - 指标环比变动情况权重70%，全国排名权重30%
                - 整体得分50分以下即为劣化
                - 打分标准：指标值环比下降每1%加1分，上升1%减1分
                - 环比小数四舍五入（如0.7%算1%）
                - 排名每上升一名加5分，下降一名减5分
                - 示例：指标A环比上升3%，排名下降2名
                  环比分=50-3=47，排名分=50-10=40
                  整体得分=47×70%+40×30%=44.9
                  得分<50需输出改进建议""",
                "[变量:评分结果=计算结果] 保存评分数据",
                """[计算:劣化指标识别] 如果所有指标均未劣化，则固定针对全国排名最低的指标标记为劣化""",
                "[调用工具:knowledge_search:劣化指标的改进建议] 查询改进方案",
                "[变量:改进建议=工具调用结果] 保存改进建议",
                "总结数据并生成完整的评分分析报告"
            ],
            response_format="""
# 📊 指标环比与排名综合评分分析报告

## 📈 数据概览
[引用:查询结果]

## 🧮 评分计算
[引用:评分结果]

## ⚠️ 劣化指标识别
[引用:劣化指标识别结果]

## 💡 改进建议
[引用:改进建议]

## 📋 总结
基于环比变动（权重70%）和全国排名（权重30%）的综合评分分析
            """,
            priority=4,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例3：多数据库查询汇总（标准化版）
        prompt3 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="多省份数据汇总分析（标准化版）",
            description="使用标准化语法进行多省份数据查询和汇总分析",
            trigger_questions=[
                "多省份标准化数据汇总",
                "标准语法省份对比",
                "规范化多省查询"
            ],
            processing_steps=[
                "[SQL查询:原始:关键业务指标] 查询本省数据",
                "[SQL查询:JT:全国汇总数据和各省排名] 查询全国数据",
                "[SQL查询:GZ:关键业务指标] 查询贵州省数据",
                "[SQL查询:BJ:关键业务指标] 查询北京市数据",
                "[变量:本省数据=步骤1结果] 保存本省数据",
                "[变量:全国数据=步骤2结果] 保存全国数据",
                "[变量:贵州数据=步骤3结果] 保存贵州数据",
                "[变量:北京数据=步骤4结果] 保存北京数据",
                "[切换数据库:原始] 切换回原始数据库进行分析",
                "[计算:多省份对比分析] 汇总所有数据进行综合分析",
                "生成多维度对比分析报告"
            ],
            response_format="""
# 📊 多省份数据汇总分析报告

## 📋 数据汇总
| 省份 | 指标1 | 指标2 | 指标3 | 排名 |
|------|-------|-------|-------|------|
| 本省 | [引用:本省数据] |
| 贵州 | [引用:贵州数据] |
| 北京 | [引用:北京数据] |
| 全国 | [引用:全国数据] |

## 📈 对比分析
[引用:计算结果]
            """,
            priority=3,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 添加示例提示词
        prompts = [prompt1, prompt2, prompt3]
        success_count = 0
        
        for prompt in prompts:
            if manager.add_complex_prompt(prompt):
                success_count += 1
                print(f"✅ 成功添加示例提示词: {prompt.title}")
            else:
                print(f"❌ 添加示例提示词失败: {prompt.title}")
        
        print(f"\n📊 总结: 成功添加 {success_count}/{len(prompts)} 个示例提示词")
        
        if success_count > 0:
            print("\n🎯 标准化语法说明:")
            print("1. [SQL查询:数据库:查询内容] - 切换数据库并执行SQL查询")
            print("2. [切换数据库:数据库] - 只切换数据库，不执行查询")
            print("3. [调用工具:工具名:参数] - 调用指定工具")
            print("4. [计算:计算逻辑] - 执行复杂计算")
            print("5. [变量:变量名=值] - 设置和保存变量")
            print("6. [引用:变量名] - 在回复格式中引用变量")
            
            print("\n💡 优势:")
            print("- ✅ 语法标准化，减少大模型理解错误")
            print("- ✅ 明确指定操作类型，避免歧义")
            print("- ✅ 支持复杂的数据传递和计算")
            print("- ✅ 可以精确控制每个步骤的行为")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 创建示例失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 标准化语法示例")
    print("=" * 60)
    
    if create_standardized_syntax_examples():
        print("\n" + "=" * 60)
        print("🎉 标准化语法示例创建完成！")
        print("\n📋 支持的标准化命令:")
        print("🔸 [SQL查询:数据库:查询内容] - 数据库查询")
        print("🔸 [切换数据库:数据库] - 数据库切换")
        print("🔸 [调用工具:工具名:参数] - 工具调用")
        print("🔸 [计算:计算逻辑] - 复杂计算")
        print("🔸 [变量:变量名=值] - 变量管理")
        print("🔸 [引用:变量名] - 结果引用")
        
        print("\n💡 这样的标准化语法可以:")
        print("- 减少大模型的理解错误")
        print("- 提高指令执行的准确性")
        print("- 支持复杂的业务逻辑")
        print("- 便于维护和扩展")
    else:
        print("\n❌ 示例创建失败，请检查配置")
