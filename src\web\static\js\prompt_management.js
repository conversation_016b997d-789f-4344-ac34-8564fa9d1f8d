/**
 * 复杂提示词管理系统前端JavaScript
 */

// API基础URL
const API_BASE = '/api/prompts';

// 全局变量
let currentPrompts = [];
let currentEditingId = null;
let currentDeletingId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载提示词列表
    refreshPromptList();
    
    // 加载统计信息
    loadStats();
    
    // 绑定事件
    bindEvents();
});

// 绑定事件
function bindEvents() {
    // 创建提示词表单
    document.getElementById('createPromptForm').addEventListener('submit', handleCreatePrompt);
    
    // 搜索表单
    document.getElementById('searchForm').addEventListener('submit', handleSearch);
    
    // 编辑保存按钮
    document.getElementById('saveEditButton').addEventListener('click', handleSaveEdit);
    
    // 确认删除按钮
    document.getElementById('confirmDeleteButton').addEventListener('click', handleConfirmDelete);
    
    // 标签页切换事件
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            if (event.target.id === 'stats-tab') {
                loadStats();
            }
        });
    });
}

// 刷新提示词列表
async function refreshPromptList() {
    try {
        console.log('🔄 开始刷新提示词列表...');
        showLoading('promptList');

        const response = await fetch(API_BASE + '/');
        console.log('📡 API响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const prompts = await response.json();
        console.log('📊 获取到的数据:', prompts);
        console.log('📊 数据类型:', typeof prompts, '是否为数组:', Array.isArray(prompts));

        if (Array.isArray(prompts) && prompts.length > 0) {
            console.log('📝 第一个提示词:', prompts[0]);
            console.log('📝 trigger_questions字段:', prompts[0].trigger_questions);
            console.log('📝 trigger_questions类型:', typeof prompts[0].trigger_questions);
        }

        currentPrompts = prompts;

        renderPromptList(prompts);

    } catch (error) {
        console.error('❌ 加载提示词列表失败:', error);
        showError('promptList', '加载提示词列表失败: ' + error.message);
    }
}

// 渲染提示词列表
function renderPromptList(prompts) {
    console.log('🎨 开始渲染提示词列表:', prompts);
    const container = document.getElementById('promptList');

    if (!prompts || prompts.length === 0) {
        console.log('📝 没有提示词数据');
        container.innerHTML = '<div class="alert alert-info">暂无复杂提示词</div>';
        return;
    }

    console.log(`📝 准备渲染 ${prompts.length} 个提示词`);

    if (prompts.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="bi bi-inbox" style="font-size: 3rem; color: #6c757d;"></i>
                <p class="mt-3 text-muted">暂无提示词，点击"创建提示词"开始添加</p>
            </div>
        `;
        return;
    }
    
    const html = prompts.map((prompt, index) => {
        console.log(`🎨 渲染提示词 ${index + 1}:`, prompt);
        console.log(`🎨 trigger_questions:`, prompt.trigger_questions);

        if (!prompt.trigger_questions) {
            console.error(`❌ 提示词 ${index + 1} 缺少 trigger_questions 字段`);
            return '';
        }

        if (!Array.isArray(prompt.trigger_questions)) {
            console.error(`❌ 提示词 ${index + 1} 的 trigger_questions 不是数组:`, typeof prompt.trigger_questions);
            return '';
        }

        return `
        <div class="col-md-6 col-lg-4">
            <div class="card position-relative">
                <span class="badge bg-primary priority-badge">优先级 ${prompt.priority}</span>
                <div class="card-header">
                    <h6 class="card-title mb-0">${escapeHtml(prompt.title)}</h6>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted">${escapeHtml(prompt.description)}</p>
                    
                    <div class="mb-2">
                        <small class="text-muted">触发问题:</small><br>
                        ${prompt.trigger_questions.map(question =>
                            `<span class="badge bg-primary question-badge">${escapeHtml(question)}</span>`
                        ).join('')}
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">处理步骤 (${prompt.processing_steps.length}个):</small>
                        <div class="processing-steps-container">
                            ${prompt.processing_steps.map((step, index) =>
                                `<div class="step-item"><small><strong>步骤${index + 1}:</strong><br>${escapeHtml(step)}</small></div>`
                            ).join('')}
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">回复格式:</small>
                        <div class="format-preview" style="max-height: 120px; font-size: 0.85rem;">
                            ${escapeHtml(prompt.response_format.substring(0, 300))}${prompt.response_format.length > 300 ? '...' : ''}
                        </div>
                    </div>

                    <div class="mb-3">
                        ${prompt.no_think_mode ?
                            '<span class="badge bg-success"><i class="bi bi-lightning"></i> 极速模式</span>' :
                            '<span class="badge bg-secondary">普通模式</span>'
                        }
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            创建: ${formatDate(prompt.created_at)}
                        </small>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="editPrompt('${prompt.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deletePrompt('${prompt.id}', '${escapeHtml(prompt.title)}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    }).join('');
    
    container.innerHTML = html;
}

// 处理创建提示词
async function handleCreatePrompt(event) {
    event.preventDefault();
    
    const formData = {
        title: document.getElementById('title').value,
        description: document.getElementById('description').value,
        trigger_questions: document.getElementById('triggerQuestions').value.split('\n').map(q => q.trim()).filter(q => q),
        processing_steps: document.getElementById('processingSteps').value.split('\n\n').map(s => s.trim()).filter(s => s),
        response_format: document.getElementById('responseFormat').value,
        priority: parseInt(document.getElementById('priority').value),
        no_think_mode: document.getElementById('noThinkMode').checked
    };
    
    try {
        const response = await fetch(API_BASE + '/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '创建失败');
        }
        
        // 清空表单
        document.getElementById('createPromptForm').reset();
        
        // 切换到列表标签页并刷新
        const listTab = new bootstrap.Tab(document.getElementById('list-tab'));
        listTab.show();
        
        await refreshPromptList();
        
        showAlert('success', '提示词创建成功！');
        
    } catch (error) {
        console.error('创建提示词失败:', error);
        showAlert('danger', '创建失败: ' + error.message);
    }
}

// 编辑提示词
function editPrompt(promptId) {
    const prompt = currentPrompts.find(p => p.id === promptId);
    if (!prompt) {
        showAlert('danger', '找不到要编辑的提示词');
        return;
    }
    
    currentEditingId = promptId;
    
    // 填充编辑表单
    document.getElementById('editPromptId').value = prompt.id;
    document.getElementById('editTitle').value = prompt.title;
    document.getElementById('editDescription').value = prompt.description;
    document.getElementById('editTriggerQuestions').value = prompt.trigger_questions.join('\n');
    document.getElementById('editProcessingSteps').value = prompt.processing_steps.join('\n\n');
    document.getElementById('editResponseFormat').value = prompt.response_format;
    document.getElementById('editPriority').value = prompt.priority;
    document.getElementById('editNoThinkMode').checked = prompt.no_think_mode || false;
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('editPromptModal'));
    modal.show();
}

// 处理保存编辑
async function handleSaveEdit() {
    if (!currentEditingId) return;
    
    const formData = {
        title: document.getElementById('editTitle').value,
        description: document.getElementById('editDescription').value,
        trigger_questions: document.getElementById('editTriggerQuestions').value.split('\n').map(q => q.trim()).filter(q => q),
        processing_steps: document.getElementById('editProcessingSteps').value.split('\n\n').map(s => s.trim()).filter(s => s),
        response_format: document.getElementById('editResponseFormat').value,
        priority: parseInt(document.getElementById('editPriority').value),
        no_think_mode: document.getElementById('editNoThinkMode').checked
    };
    
    try {
        const response = await fetch(`${API_BASE}/${currentEditingId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '更新失败');
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editPromptModal'));
        modal.hide();
        
        // 刷新列表
        await refreshPromptList();
        
        showAlert('success', '提示词更新成功！');
        
    } catch (error) {
        console.error('更新提示词失败:', error);
        showAlert('danger', '更新失败: ' + error.message);
    }
}

// 删除提示词
function deletePrompt(promptId, promptTitle) {
    currentDeletingId = promptId;
    document.getElementById('deletePromptTitle').textContent = promptTitle;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// 确认删除
async function handleConfirmDelete() {
    if (!currentDeletingId) return;
    
    try {
        const response = await fetch(`${API_BASE}/${currentDeletingId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '删除失败');
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        
        // 刷新列表
        await refreshPromptList();
        
        showAlert('success', '提示词删除成功！');
        
    } catch (error) {
        console.error('删除提示词失败:', error);
        showAlert('danger', '删除失败: ' + error.message);
    }
}

// 处理搜索
async function handleSearch(event) {
    event.preventDefault();
    
    const query = document.getElementById('searchQuery').value;
    const k = parseInt(document.getElementById('resultCount').value);
    const scoreThreshold = parseFloat(document.getElementById('scoreThreshold').value);
    
    try {
        showLoading('searchResults');
        
        const response = await fetch(`${API_BASE}/search`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: query,
                k: k,
                score_threshold: scoreThreshold
            })
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '搜索失败');
        }
        
        const searchResult = await response.json();
        renderSearchResults(searchResult);
        
    } catch (error) {
        console.error('搜索失败:', error);
        showError('searchResults', '搜索失败: ' + error.message);
    }
}

// 渲染搜索结果
function renderSearchResults(searchResult) {
    const container = document.getElementById('searchResults');
    
    if (searchResult.results.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                没有找到匹配的提示词。尝试调整搜索词或降低相似度阈值。
            </div>
        `;
        return;
    }
    
    const html = `
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            找到 ${searchResult.total_count} 个匹配结果，查询: "${escapeHtml(searchResult.query)}"
        </div>
        ${searchResult.results.map((result, index) => `
            <div class="card search-result position-relative mb-3">
                <div class="similarity-score">
                    <span class="badge bg-success">相似度: ${(result.similarity * 100).toFixed(1)}%</span>
                </div>
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <span class="badge bg-primary me-2">#${index + 1}</span>
                        ${escapeHtml(result.prompt.title)}
                        <span class="badge bg-secondary ms-2">优先级 ${result.prompt.priority}</span>
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text">${escapeHtml(result.prompt.description)}</p>
                    
                    <div class="mb-2">
                        <small class="text-muted">触发问题:</small><br>
                        ${result.prompt.trigger_questions.map(question =>
                            `<span class="badge bg-primary question-badge">${escapeHtml(question)}</span>`
                        ).join('')}
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">处理步骤:</small>
                        <div style="max-height: 150px; overflow-y: auto;">
                            ${result.prompt.processing_steps.map((step, stepIndex) => 
                                `<div class="step-item"><small>${stepIndex + 1}. ${escapeHtml(step)}</small></div>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `).join('')}
    `;
    
    container.innerHTML = html;
}

// 加载统计信息
async function loadStats() {
    try {
        showLoading('statsContent');
        
        const response = await fetch(`${API_BASE}/stats/summary`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const stats = await response.json();
        renderStats(stats);
        
    } catch (error) {
        console.error('加载统计信息失败:', error);
        showError('statsContent', '加载统计信息失败: ' + error.message);
    }
}

// 渲染统计信息
function renderStats(stats) {
    const priorityChart = Object.entries(stats.priority_distribution)
        .map(([priority, count]) => `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span>优先级 ${priority}</span>
                <span class="badge bg-primary">${count}</span>
            </div>
        `).join('');
    
    const html = `
        <div class="row">
            <div class="col-md-6">
                <h5><i class="bi bi-bar-chart"></i> 基本统计</h5>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>总提示词数量</span>
                        <span class="badge bg-success fs-6">${stats.total_prompts}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>数据库路径</span>
                        <small class="text-muted">${stats.database_path}</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>集合名称</span>
                        <small class="text-muted">${stats.collection_name}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h5><i class="bi bi-pie-chart"></i> 优先级分布</h5>
                <div class="mb-3">
                    ${priorityChart}
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('statsContent').innerHTML = html;
}

// 工具函数
function showLoading(containerId) {
    document.getElementById(containerId).innerHTML = `
        <div class="text-center py-5">
            <div class="loading"></div>
            <p class="mt-2">加载中...</p>
        </div>
    `;
}

function showError(containerId, message) {
    document.getElementById(containerId).innerHTML = `
        <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle"></i>
            ${escapeHtml(message)}
        </div>
    `;
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
            ${escapeHtml(message)}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // 在页面顶部显示警告
    const container = document.querySelector('.container');
    const alertDiv = document.createElement('div');
    alertDiv.innerHTML = alertHtml;
    container.insertBefore(alertDiv.firstElementChild, container.firstElementChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 3000);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}
