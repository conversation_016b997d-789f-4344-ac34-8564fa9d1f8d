"""
向量存储管理器
支持多种向量数据库：Chroma、FAISS、Pinecone、Weaviate等
"""

from typing import List, Optional, Dict, Any
from langchain_core.vectorstores import VectorStore
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document
from langchain_community.vectorstores import (
    Chroma,
    FAISS,
    Pinecone,
    Weaviate
)
import tempfile
import os

from ..config import settings


class VectorStoreManager:
    """向量存储管理器"""
    
    @staticmethod
    def create_vectorstore(
        store_type: str,
        embeddings: Embeddings,
        documents: Optional[List[Document]] = None,
        **kwargs
    ) -> VectorStore:
        """
        创建向量存储
        
        Args:
            store_type: 存储类型 (chroma, faiss, pinecone, weaviate)
            embeddings: 嵌入模型
            documents: 文档列表
            **kwargs: 其他参数
            
        Returns:
            VectorStore: 向量存储实例
        """
        if store_type.lower() == "chroma":
            return VectorStoreManager._create_chroma(embeddings, documents, **kwargs)
        elif store_type.lower() == "faiss":
            return VectorStoreManager._create_faiss(embeddings, documents, **kwargs)
        elif store_type.lower() == "pinecone":
            return VectorStoreManager._create_pinecone(embeddings, documents, **kwargs)
        elif store_type.lower() == "weaviate":
            return VectorStoreManager._create_weaviate(embeddings, documents, **kwargs)
        else:
            raise ValueError(f"不支持的向量存储类型: {store_type}")
    
    @staticmethod
    def _create_chroma(
        embeddings: Embeddings,
        documents: Optional[List[Document]] = None,
        **kwargs
    ) -> Chroma:
        """创建Chroma向量存储"""
        persist_directory = kwargs.get("persist_directory", "./data/chroma_db")
        collection_name = kwargs.get("collection_name", "langchain")
        
        if documents:
            return Chroma.from_documents(
                documents=documents,
                embedding=embeddings,
                persist_directory=persist_directory,
                collection_name=collection_name
            )
        else:
            return Chroma(
                embedding_function=embeddings,
                persist_directory=persist_directory,
                collection_name=collection_name
            )
    
    @staticmethod
    def _create_faiss(
        embeddings: Embeddings,
        documents: Optional[List[Document]] = None,
        **kwargs
    ) -> FAISS:
        """创建FAISS向量存储"""
        if documents:
            return FAISS.from_documents(documents, embeddings)
        else:
            # 创建空的FAISS索引
            temp_doc = Document(page_content="临时文档", metadata={})
            vectorstore = FAISS.from_documents([temp_doc], embeddings)
            # 删除临时文档
            vectorstore.delete([vectorstore.index_to_docstore_id[0]])
            return vectorstore
    
    @staticmethod
    def _create_pinecone(
        embeddings: Embeddings,
        documents: Optional[List[Document]] = None,
        **kwargs
    ) -> Pinecone:
        """创建Pinecone向量存储"""
        if not settings.pinecone_api_key:
            raise ValueError("未设置Pinecone API密钥")
        
        index_name = kwargs.get("index_name", "langchain-index")
        
        import pinecone
        pinecone.init(
            api_key=settings.pinecone_api_key,
            environment=settings.pinecone_environment
        )
        
        if documents:
            return Pinecone.from_documents(
                documents, embeddings, index_name=index_name
            )
        else:
            return Pinecone.from_existing_index(index_name, embeddings)
    
    @staticmethod
    def _create_weaviate(
        embeddings: Embeddings,
        documents: Optional[List[Document]] = None,
        **kwargs
    ) -> Weaviate:
        """创建Weaviate向量存储"""
        import weaviate
        
        client = weaviate.Client(
            url=settings.weaviate_url,
            auth_client_secret=weaviate.AuthApiKey(settings.weaviate_api_key) 
            if settings.weaviate_api_key else None
        )
        
        index_name = kwargs.get("index_name", "LangChain")
        
        if documents:
            return Weaviate.from_documents(
                documents, embeddings, client=client, index_name=index_name
            )
        else:
            return Weaviate(client, index_name, "text", embeddings)
    
    @staticmethod
    def save_vectorstore(vectorstore: VectorStore, path: str, store_type: str):
        """保存向量存储"""
        if store_type.lower() == "faiss":
            vectorstore.save_local(path)
        elif store_type.lower() == "chroma":
            # Chroma会自动持久化到指定目录
            pass
        else:
            print(f"警告: {store_type} 类型的向量存储不支持本地保存")
    
    @staticmethod
    def load_vectorstore(
        path: str, 
        embeddings: Embeddings, 
        store_type: str
    ) -> VectorStore:
        """加载向量存储"""
        if store_type.lower() == "faiss":
            return FAISS.load_local(path, embeddings)
        elif store_type.lower() == "chroma":
            return Chroma(persist_directory=path, embedding_function=embeddings)
        else:
            raise ValueError(f"不支持加载 {store_type} 类型的向量存储") 