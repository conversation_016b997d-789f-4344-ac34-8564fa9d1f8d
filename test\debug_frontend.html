<!DOCTYPE html>
<html>
<head>
    <title>调试前端</title>
</head>
<body>
    <h1>调试前端数据</h1>
    <div id="result"></div>

    <script>
        async function testAPI() {
            try {
                console.log('开始测试API...');
                
                const response = await fetch('/api/prompts');
                console.log('API响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('API返回数据:', data);
                
                if (Array.isArray(data)) {
                    console.log(`获取到 ${data.length} 个提示词`);
                    
                    data.forEach((prompt, index) => {
                        console.log(`提示词 ${index + 1}:`, prompt);
                        
                        // 检查字段
                        if (prompt.trigger_questions) {
                            console.log(`✅ trigger_questions存在:`, prompt.trigger_questions);
                            if (Array.isArray(prompt.trigger_questions)) {
                                console.log(`✅ trigger_questions是数组，长度: ${prompt.trigger_questions.length}`);
                            } else {
                                console.error(`❌ trigger_questions不是数组:`, typeof prompt.trigger_questions);
                            }
                        } else {
                            console.error(`❌ trigger_questions不存在`);
                        }
                        
                        if (prompt.trigger_keywords) {
                            console.warn(`⚠️ 发现旧字段 trigger_keywords:`, prompt.trigger_keywords);
                        }
                    });
                } else {
                    console.error('❌ API返回的不是数组:', typeof data);
                }
                
                document.getElementById('result').innerHTML = `
                    <h2>测试结果</h2>
                    <p>获取到 ${data.length} 个提示词</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('❌ API测试失败:', error);
                document.getElementById('result').innerHTML = `
                    <h2>错误</h2>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
        
        // 页面加载后自动测试
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
