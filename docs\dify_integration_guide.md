# SQL Agent API 服务器 - Dify 平台接入指南

## 概述

本指南将帮助您将 SQL Agent API 服务器集成到 Dify 平台中，实现自然语言到 SQL 查询的智能服务。

## 功能特性

✅ **自然语言转SQL**: 将用户的自然语言问题转换为精确的SQL查询  
✅ **自动执行查询**: 自动连接数据库并执行生成的SQL  
✅ **智能结果解读**: 将查询结果转换为易懂的自然语言回答  
✅ **会话管理**: 支持多用户会话和上下文保持  
✅ **OpenAI兼容**: 完全兼容OpenAI API格式，无缝接入Dify  

## 快速开始

### 1. 启动SQL Agent服务器

```bash
# 方式1: 使用启动脚本
python start_sql_agent_server.py

# 方式2: 直接运行
python examples/sql_agent_server.py
```

服务器默认监听端口 `8001`，启动后可访问：
- API文档: http://localhost:8001/docs
- 健康检查: http://localhost:8001/health

### 2. 在Dify中配置模型提供商

#### 步骤1: 添加自定义模型提供商
1. 登录Dify管理后台
2. 进入 **设置** > **模型提供商**
3. 点击 **添加模型提供商**
4. 选择 **自定义模型**

#### 步骤2: 配置模型参数
```
模型提供商名称: SQL Agent
API Base URL: http://localhost:8001/v1
API Key: sql-agent-key (任意字符串)
模型名称: sql-agent
```

#### 步骤3: 测试连接
点击 **测试** 按钮，确保连接正常。

### 3. 创建SQL查询应用

#### 步骤1: 创建新应用
1. 在Dify中创建新的 **聊天助手** 应用
2. 选择刚配置的 **SQL Agent** 模型

#### 步骤2: 配置系统提示词
```
你是一个专业的数据库查询助手。你能够：
1. 理解用户的自然语言问题
2. 将问题转换为精确的SQL查询
3. 执行查询并获取结果
4. 用通俗易懂的语言解释查询结果

请始终保持专业、准确和友善的态度。
```

#### 步骤3: 设置模型参数
- **Temperature**: 0.1 (保证查询的一致性)
- **Max Tokens**: 1000
- **Top P**: 0.9

## 使用示例

### 基本查询示例

**用户输入**: "2025年4月电费是多少"

**系统响应**:
```
根据查询结果，2025年4月的电费为 156.78 元。

生成的SQL查询：
SELECT amount FROM electricity_bills WHERE bill_date = '2025-04-01'

查询返回1条记录，显示4月份电费金额为156.78元。
```

### 对比查询示例

**用户输入**: "2025年3月和4月的电费对比"

**系统响应**:
```
2025年3月和4月电费对比：
- 3月电费：142.35元
- 4月电费：156.78元
- 差额：+14.43元（4月比3月高10.1%）

生成的SQL查询：
SELECT bill_date, amount FROM electricity_bills 
WHERE bill_date IN ('2025-03-01', '2025-04-01')
ORDER BY bill_date
```

## API 端点说明

### 1. SQL查询端点 (推荐)
```
POST /v1/sql/query
```

**请求格式**:
```json
{
  "question": "2025年4月电费是多少",
  "session_id": "user-session-123",
  "include_sql": true,
  "include_data": true
}
```

**响应格式**:
```json
{
  "success": true,
  "answer": "根据查询结果，2025年4月的电费为156.78元。",
  "generated_sql": "SELECT amount FROM electricity_bills WHERE bill_date = '2025-04-01'",
  "data": [{"amount": 156.78}],
  "row_count": 1,
  "execution_time_ms": 234.5,
  "session_id": "user-session-123"
}
```

### 2. OpenAI兼容端点
```
POST /v1/chat/completions
```

**请求格式**:
```json
{
  "model": "sql-agent",
  "messages": [
    {"role": "user", "content": "2025年4月电费是多少"}
  ]
}
```

**响应格式**:
```json
{
  "id": "sqlcmpl-xxx",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "sql-agent",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "根据查询结果，2025年4月的电费为156.78元。"
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 15,
    "completion_tokens": 25,
    "total_tokens": 40
  }
}
```

## 高级配置

### 会话管理

支持多用户会话隔离：
```json
{
  "question": "继续查询5月份的数据",
  "session_id": "user-123"
}
```

### 自定义数据库类型

```json
{
  "question": "查询用户数据",
  "database_type": "postgresql"
}
```

### 控制响应内容

```json
{
  "question": "查询数据",
  "include_sql": false,    // 不返回SQL
  "include_data": false    // 不返回原始数据
}
```

## 错误处理

### 常见错误及解决方案

1. **连接失败**
   ```json
   {
     "success": false,
     "error": "SQL Agent未初始化",
     "answer": "服务暂时不可用，请稍后重试"
   }
   ```
   **解决**: 检查服务器是否正常运行

2. **SQL生成失败**
   ```json
   {
     "success": false,
     "error": "SQL生成失败: API请求超时",
     "answer": "抱歉，无法理解您的问题，请尝试重新表述"
   }
   ```
   **解决**: 检查SQL生成API服务状态

3. **数据库连接失败**
   ```json
   {
     "success": false,
     "error": "MySQL执行失败: 连接被拒绝",
     "answer": "数据库连接异常，请联系管理员"
   }
   ```
   **解决**: 检查数据库配置和网络连接

## 性能优化

### 1. 连接池配置
```python
# 在.env文件中配置
MYSQL_POOL_SIZE=10
MYSQL_MAX_OVERFLOW=20
```

### 2. 缓存策略
- SQL查询结果缓存
- 会话上下文缓存
- 模型响应缓存

### 3. 监控指标
```
GET /health
```
返回服务健康状态和性能指标。

## 安全考虑

### 1. SQL注入防护
- 只允许SELECT查询
- 禁止危险关键词
- 参数化查询

### 2. 访问控制
- API密钥验证
- IP白名单
- 请求频率限制

### 3. 数据隐私
- 敏感数据脱敏
- 查询日志审计
- 会话数据加密

## 故障排除

### 1. 服务启动失败
```bash
# 检查配置文件
cat .env

# 检查依赖安装
pip list | grep -E "(fastapi|uvicorn|pymysql)"

# 查看详细错误
python examples/sql_agent_server.py
```

### 2. 连接测试
```bash
# 健康检查
curl http://localhost:8001/health

# 测试查询
curl -X POST http://localhost:8001/v1/sql/query \
  -H "Content-Type: application/json" \
  -d '{"question": "测试查询"}'
```

### 3. 日志分析
服务器日志位置：
- 启动日志: 控制台输出
- 查询日志: 应用日志
- 错误日志: error.log

## 部署建议

### 1. 生产环境部署
```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker examples.sql_agent_server:app --bind 0.0.0.0:8001

# 使用Docker部署
docker build -t sql-agent-server .
docker run -p 8001:8001 --env-file .env sql-agent-server
```

### 2. 负载均衡
- 使用Nginx进行负载均衡
- 配置健康检查端点
- 实现故障转移

### 3. 监控和告警
- 接入Prometheus监控
- 配置Grafana仪表板
- 设置告警规则

## 常见问题

**Q: 如何修改默认端口？**
A: 在.env文件中设置 `API_SERVER_PORT=8002`

**Q: 支持哪些数据库？**
A: 目前支持MySQL，计划支持PostgreSQL、SQLite

**Q: 如何添加自定义数据表？**
A: 修改数据库配置，确保表结构符合查询需求

**Q: 可以自定义SQL生成模型吗？**
A: 可以，在.env中配置 `SQL_GENERATOR_API_URL`

**Q: 如何处理大数据量查询？**
A: 系统会自动限制返回行数，建议添加分页机制

## 联系支持

如果您在使用过程中遇到问题，请：
1. 查看API文档: http://localhost:8001/docs
2. 运行测试脚本: `python examples/test_sql_agent_client.py`
3. 检查服务器日志获取详细错误信息

---

📝 **文档版本**: v1.0.0  
🔄 **最后更新**: 2024年1月  
📧 **技术支持**: 请提交Issue或联系开发团队 