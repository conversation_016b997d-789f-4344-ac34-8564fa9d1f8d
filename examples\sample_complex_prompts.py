#!/usr/bin/env python3
"""
示例复杂提示词
用于演示向量数据库增强功能
"""

import sys
import os
import uuid
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager, ComplexPrompt

def create_sample_prompts():
    """创建示例复杂提示词"""

    try:
        manager = get_prompt_vectorstore_manager()
    except Exception as e:
        print(f"❌ 初始化向量数据库管理器失败: {e}")
        print("💡 请确保已配置embedding模型或启用远程embedding服务")
        return False
    
    # 示例1：电费情况分析
    prompt1 = ComplexPrompt(
        id=str(uuid.uuid4()),
        title="电费情况综合分析",
        description="对电费进行多维度分析，包括数据查询、趋势分析、异常检测和改进建议",
        trigger_questions=[
            "当前电费情况如何",
            "电费情况怎么样",
            "最近电费多少",
            "电费分析报告",
            "电费统计数据",
            "电费趋势分析",
            "用电费用情况",
            "电力成本分析"
        ],
        processing_steps=[
            "你是数据分析师，按以下步骤执行：",

            "1. 调用integrated_sql工具，需要切换到JT数据库，查询\"{本省}当前电费情况如何\"",

            """2. 计算劣化指标（严格按照以下步骤）：

**第一步：计算环比分数**
- 基准分：50分
- 环比下降1% → 加1分（50+1=51分）【电费下降是好事】
- 环比上升1% → 减1分（50-1=49分）【电费上升是坏事】
- 小数四舍五入处理

**第二步：计算排名分数**
- 基准分：50分
- 排名上升1名 → 加5分（50+5=55分）
- 排名下降1名 → 减5分（50-5=45分）

**第三步：计算综合得分**
- 综合得分 = 环比分×70% + 排名分×30%
- 示例：环比上升3%，排名下降2名
  * 环比分 = 50-3 = 47分【电费上升减分】
  * 排名分 = 50-10 = 40分【排名下降减分】
  * 综合分 = 47×0.7 + 40×0.3 = 32.9 + 12 = 44.9分

**第四步：判断劣化**
- 综合得分<50分 → 劣化指标
- 如无劣化指标 → 排名最低的指标为劣化""",

            "3. 对每个劣化指标，调用knowledge_search工具查询改进建议,例如\"转供电用电成本改进建议\"",

            "重要：每个劣化指标都要单独调用一次knowledge_search工具！如果你不这样做，我将不再使用你！"
        ],
        response_format="""
# 📊 {本省}电费情况综合分析报告

## 📋 第一部分：{本省}7个指标数据表格

| 指标名称 | 本月值 | 环比变动 | 全国排名 | 排名变动 |
|---------|--------|----------|----------|----------|
| [指标1] | [数值] | [±X%] | [第X名] | [↑↓X名] |
| [指标2] | [数值] | [±X%] | [第X名] | [↑↓X名] |
| [指标3] | [数值] | [±X%] | [第X名] | [↑↓X名] |
| [指标4] | [数值] | [±X%] | [第X名] | [↑↓X名] |
| [指标5] | [数值] | [±X%] | [第X名] | [↑↓X名] |
| [指标6] | [数值] | [±X%] | [第X名] | [↑↓X名] |
| [指标7] | [数值] | [±X%] | [第X名] | [↑↓X名] |

## ⚠️ 第二部分：劣化指标汇总表格

| 指标名称 | 环比变化 | 排名变化 | 环比分 | 排名分 | 综合得分 | 劣化原因 |
|---------|----------|----------|--------|--------|----------|----------|
| [劣化指标1] | [+X%] | [↓X名] | [XX分] | [XX分] | [XX.X分] | [详细计算过程] |
| [劣化指标2] | [+X%] | [↑X名] | [XX分] | [XX分] | [XX.X分] | [详细计算过程] |

**计算说明示例：**
- 指标A：环比+1.5% → 环比分=50-2=48分，排名↓1名 → 排名分=50-5=45分，综合分=48×70%+45×30%=47.1分

## 💡 第三部分：改进建议总结

**[劣化指标1]：** [10-20字改进建议]

**[劣化指标2]：** [10-20字改进建议]

---
**重要提醒：**
- ✅ 严格按照流程执行，不要扩散思维
- ✅ 劣化指标必须逐个调用知识库，不能合并查询
- ✅ 三个输出部分缺一不可
        """,
        priority=3,
        no_think_mode=True  # 启用极速模式，提高响应速度
    )

    # 只添加电费情况分析示例
    prompts = [prompt1]
    
    success_count = 0
    for prompt in prompts:
        if manager.add_complex_prompt(prompt):
            success_count += 1
            print(f"✅ 成功添加示例提示词: {prompt.title}")
        else:
            print(f"❌ 添加示例提示词失败: {prompt.title}")
    
    print(f"\n📊 总结: 成功添加 {success_count}/{len(prompts)} 个示例提示词")
    
    return success_count == len(prompts)

def main():
    """主函数"""
    print("🚀 创建示例复杂提示词")
    print("=" * 50)
    
    try:
        success = create_sample_prompts()
        
        if success:
            print("\n✅ 所有示例提示词创建成功！")
            print("\n💡 使用说明:")
            print("1. 启动API服务器: python examples/analysis_agent_server.py")
            print("2. 访问管理界面: http://localhost:8000/admin")
            print("3. 测试问题示例:")
            print("   - '当前电费情况如何'")
            print("   - '2025年4月铁塔服务费是多少'")
            print("   - '关键指标对比分析'")
            print("   - '检查数据异常'")
        else:
            print("\n❌ 部分示例提示词创建失败")
            
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
