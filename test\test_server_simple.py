#!/usr/bin/env python3
"""
简单的服务器测试
"""

import requests
import json
import time

def test_server():
    """测试服务器"""
    print("🧪 测试服务器")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    try:
        # 测试健康检查
        print("🔍 测试健康检查...")
        response = requests.get("http://localhost:8001/health", timeout=5)
        print(f"健康检查状态: {response.status_code}")
        
        # 测试API
        print("🔍 测试提示词API...")
        response = requests.get("http://localhost:8001/api/prompts", timeout=10)
        print(f"API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取到 {len(data)} 个提示词")
            
            if data:
                first_prompt = data[0]
                print(f"📝 第一个提示词:")
                print(f"  标题: {first_prompt.get('title', 'N/A')}")
                print(f"  触发问题: {first_prompt.get('trigger_questions', 'N/A')}")
                
                # 检查字段
                required_fields = ['id', 'title', 'description', 'trigger_questions', 'processing_steps']
                missing_fields = []
                for field in required_fields:
                    if field not in first_prompt:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"❌ 缺少字段: {missing_fields}")
                else:
                    print("✅ 所有必需字段都存在")
                    
                # 检查trigger_questions是否为数组
                trigger_questions = first_prompt.get('trigger_questions')
                if isinstance(trigger_questions, list):
                    print(f"✅ trigger_questions是数组，长度: {len(trigger_questions)}")
                else:
                    print(f"❌ trigger_questions不是数组: {type(trigger_questions)}")
            
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_server()
