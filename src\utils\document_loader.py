"""
文档加载工具
支持多种文件格式：PDF、Word、TXT、CSV、JSON等
"""

import os
from typing import List, Optional, Union
from pathlib import Path
from langchain_core.documents import Document
from langchain_community.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    CSVLoader,
    JSONLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader
)
from langchain.text_splitter import (
    RecursiveCharacterTextSplitter,
    CharacterTextSplitter,
    TokenTextSplitter
)


class DocumentLoader:
    """文档加载器"""
    
    SUPPORTED_EXTENSIONS = {
        '.pdf': PyPDFLoader,
        '.docx': Docx2txtLoader,
        '.txt': TextLoader,
        '.csv': CSVLoader,
        '.json': JSONLoader,
        '.html': UnstructuredHTMLLoader,
        '.md': UnstructuredMarkdownLoader
    }
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        初始化文档加载器
        
        Args:
            chunk_size: 文档块大小
            chunk_overlap: 文档块重叠大小
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", " ", ""]
        )
    
    def load_file(self, file_path: Union[str, Path]) -> List[Document]:
        """
        加载单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[Document]: 文档列表
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        extension = file_path.suffix.lower()
        
        if extension not in self.SUPPORTED_EXTENSIONS:
            raise ValueError(f"不支持的文件格式: {extension}")
        
        # 选择对应的加载器
        loader_class = self.SUPPORTED_EXTENSIONS[extension]
        
        # 特殊处理不同的加载器
        if extension == '.json':
            loader = loader_class(file_path, jq_schema='.')
        else:
            loader = loader_class(str(file_path))
        
        # 加载文档
        documents = loader.load()
        
        # 添加文件路径到元数据
        for doc in documents:
            doc.metadata['source_file'] = str(file_path)
            doc.metadata['file_type'] = extension
        
        return documents
    
    def load_directory(
        self, 
        directory_path: Union[str, Path],
        recursive: bool = True,
        file_extensions: Optional[List[str]] = None
    ) -> List[Document]:
        """
        加载目录中的所有支持文件
        
        Args:
            directory_path: 目录路径
            recursive: 是否递归加载子目录
            file_extensions: 指定文件扩展名列表
            
        Returns:
            List[Document]: 文档列表
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists() or not directory_path.is_dir():
            raise NotADirectoryError(f"目录不存在: {directory_path}")
        
        if file_extensions is None:
            file_extensions = list(self.SUPPORTED_EXTENSIONS.keys())
        
        documents = []
        
        # 获取文件列表
        if recursive:
            files = [f for f in directory_path.rglob("*") if f.is_file()]
        else:
            files = [f for f in directory_path.glob("*") if f.is_file()]
        
        # 过滤支持的文件格式
        supported_files = [
            f for f in files 
            if f.suffix.lower() in file_extensions
        ]
        
        # 加载每个文件
        for file_path in supported_files:
            try:
                file_documents = self.load_file(file_path)
                documents.extend(file_documents)
                print(f"已加载文件: {file_path}")
            except Exception as e:
                print(f"加载文件失败 {file_path}: {str(e)}")
        
        return documents
    
    def split_documents(
        self, 
        documents: List[Document],
        splitter_type: str = "recursive"
    ) -> List[Document]:
        """
        分割文档
        
        Args:
            documents: 文档列表
            splitter_type: 分割器类型 (recursive, character, token)
            
        Returns:
            List[Document]: 分割后的文档列表
        """
        if splitter_type == "character":
            splitter = CharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                separator="\n"
            )
        elif splitter_type == "token":
            splitter = TokenTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap
            )
        else:  # recursive
            splitter = self.text_splitter
        
        return splitter.split_documents(documents)
    
    def load_and_split(
        self, 
        file_or_directory: Union[str, Path],
        splitter_type: str = "recursive",
        **kwargs
    ) -> List[Document]:
        """
        加载并分割文档
        
        Args:
            file_or_directory: 文件或目录路径
            splitter_type: 分割器类型
            **kwargs: 其他参数
            
        Returns:
            List[Document]: 分割后的文档列表
        """
        path = Path(file_or_directory)
        
        if path.is_file():
            documents = self.load_file(path)
        elif path.is_dir():
            documents = self.load_directory(path, **kwargs)
        else:
            raise ValueError(f"路径不存在: {path}")
        
        return self.split_documents(documents, splitter_type)
    
    @staticmethod
    def create_document_from_text(
        text: str, 
        metadata: Optional[dict] = None
    ) -> Document:
        """
        从文本创建文档
        
        Args:
            text: 文本内容
            metadata: 元数据
            
        Returns:
            Document: 文档对象
        """
        if metadata is None:
            metadata = {}
        
        return Document(page_content=text, metadata=metadata) 