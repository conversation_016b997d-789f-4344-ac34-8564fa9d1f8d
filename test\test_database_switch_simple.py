#!/usr/bin/env python3
"""
简化的数据库切换功能测试
只测试解析功能，不需要LLM实例
"""

import sys
import os
import re

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def step_needs_sql_query(step):
    """判断步骤是否需要执行SQL查询"""
    step_lower = step.lower()

    # 定义需要SQL查询的关键词
    sql_keywords = [
        "查询", "获取", "统计", "检索", "搜索", "提取", "收集",
        "求和", "统计数据", "获取数据", "查找", "筛选"
    ]

    # 定义不需要SQL查询的关键词（分析类操作）
    analysis_keywords = [
        "分析", "对比", "比较", "评估", "汇总", "整理", "生成", "制作",
        "总结", "归纳", "梳理", "处理", "计算差异", "分析趋势", "综合分析",
        "基于", "进行", "汇总所有", "综合"
    ]

    # 优先检查分析类关键词（不需要SQL）
    for keyword in analysis_keywords:
        if keyword in step_lower:
            return False

    # 检查SQL查询关键词
    for keyword in sql_keywords:
        if keyword in step_lower:
            return True

    # 默认情况：如果包含数据库切换，通常是为了查询
    return True

def parse_database_switch_commands(processing_steps, original_province_code="GZ"):
    """
    解析处理步骤中的数据库切换命令（改进版本）
    区分是否需要执行SQL查询
    """
    enhanced_steps = []
    current_database_override = None

    for step in processing_steps:
        # 检查是否包含数据库切换命令
        switch_pattern = r'\[切换数据库[:：]([^\]]+)\]'
        match = re.search(switch_pattern, step)

        if match:
            target_db = match.group(1).strip()

            # 移除切换命令，保留其余内容
            clean_step = re.sub(switch_pattern, '', step).strip()

            # 判断是否需要执行SQL查询
            needs_sql = step_needs_sql_query(clean_step)

            print(f"🔄 [数据库切换] 检测到数据库切换命令: {target_db}")
            print(f"📊 [SQL判断] 步骤 '{clean_step}' 需要SQL查询: {needs_sql}")

            # 根据目标数据库生成相应的指令
            if target_db.upper() == "原始" or target_db.upper() == "ORIGINAL":
                # 切换回原始数据库
                if needs_sql:
                    enhanced_steps.append(f"[内部指令-SQL] 切换到原始数据库: {original_province_code}")
                else:
                    enhanced_steps.append(f"[内部指令-分析] 切换到原始数据库: {original_province_code}")
                current_database_override = None
            else:
                # 切换到指定数据库
                if needs_sql:
                    enhanced_steps.append(f"[内部指令-SQL] 切换到数据库: {target_db}")
                else:
                    enhanced_steps.append(f"[内部指令-分析] 切换到数据库: {target_db}")
                current_database_override = target_db

            # 添加清理后的步骤
            if clean_step:
                enhanced_steps.append(clean_step)
        else:
            # 普通步骤，直接添加
            enhanced_steps.append(step)

    return enhanced_steps

def modify_system_message_for_database_switch(original_system, target_database):
    """
    修改system消息以支持数据库切换（简化版本）
    """
    if not original_system:
        return f"数据库:{target_database}"
    
    # 替换现有的数据库配置
    pattern = r'数据库\s*[:：]\s*[A-Z]{1,3}'
    if re.search(pattern, original_system):
        modified_system = re.sub(pattern, f'数据库:{target_database}', original_system)
    else:
        # 如果没有数据库配置，添加一个
        modified_system = f"{original_system} 数据库:{target_database}"
    
    print(f"🔄 [数据库切换] 修改system消息: {original_system} → {modified_system}")
    return modified_system

def test_database_switch_parsing():
    """测试数据库切换命令解析"""
    print("🧪 测试数据库切换命令解析")
    print("=" * 60)
    
    # 测试各种数据库切换命令
    test_steps = [
        "查询本省基础数据",
        "[切换数据库:JT] 查询全国汇总数据",
        "[切换数据库：BJ] 查询北京数据（测试中文冒号）",
        "分析数据差异",
        "[切换数据库:原始] 切换回原始数据库",
        "生成综合报告"
    ]
    
    print("📝 测试处理步骤:")
    for i, step in enumerate(test_steps, 1):
        print(f"  {i}. {step}")
    
    # 解析数据库切换命令
    enhanced_steps = parse_database_switch_commands(test_steps, "GZ")
    
    print("\n🔄 解析后的步骤:")
    for i, step in enumerate(enhanced_steps, 1):
        if step.startswith("[内部指令]"):
            print(f"  {i}. 🔄 {step}")
        else:
            print(f"  {i}. {step}")
    
    print("\n✅ 数据库切换命令解析测试完成")

def test_system_message_modification():
    """测试system消息修改"""
    print("\n🧪 测试system消息修改")
    print("=" * 60)
    
    # 测试不同的原始system消息
    test_cases = [
        ("数据库:GZ", "JT"),
        ("数据库:GZ 其他配置", "BJ"),
        ("其他配置 数据库：SH", "JT"),
        ("没有数据库配置", "GZ"),
        ("", "JT")
    ]
    
    for original, target in test_cases:
        modified = modify_system_message_for_database_switch(original, target)
        print(f"  原始: '{original}' → 目标: {target}")
        print(f"  结果: '{modified}'")
        print()

def test_sql_judgment():
    """测试SQL查询判断逻辑"""
    print("\n🧪 测试SQL查询判断逻辑")
    print("=" * 60)

    test_cases = [
        # 需要SQL查询的步骤
        ("查询全国电费数据", True),
        ("获取贵州省统计信息", True),
        ("统计各省排名", True),
        ("检索历史数据", True),
        ("查询全国各省电费平均水平和排名数据", True),
        ("查询贵州省对比数据", True),

        # 不需要SQL查询的步骤
        ("分析数据差异", False),
        ("对比本省与全国水平", False),
        ("汇总所有数据进行综合分析", False),
        ("基于全国数据对比分析本省电费水平", False),
        ("生成分析报告", False),
        ("评估电费合理性", False),
        ("整理查询结果", False),
        ("综合分析各项指标", False),

        # 边界情况
        ("查询并分析数据", False),  # 分析类关键词优先级更高
        ("分析查询结果", False),   # 分析类优先级更高
    ]

    print("📊 SQL查询判断测试:")
    for step, expected in test_cases:
        result = step_needs_sql_query(step)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{step}' → 需要SQL: {result} (期望: {expected})")

    print("\n✅ SQL查询判断测试完成")

def test_real_world_examples():
    """测试真实世界的示例"""
    print("\n🧪 测试真实世界示例")
    print("=" * 60)
    
    # 示例1：省份对比分析
    example1_steps = [
        "查询本省最近3个月的电费数据，包括总额、单价、构成",
        "[切换数据库:JT] 查询全国各省电费平均水平和排名数据",
        "[切换数据库:原始] 基于全国数据对比分析本省电费水平",
        "计算本省电费与全国平均的差异百分比",
        "分析本省电费的优势和劣势",
        "提供电费优化建议和改进方向"
    ]
    
    print("📊 示例1: 省份电费对比分析")
    print("原始步骤:")
    for i, step in enumerate(example1_steps, 1):
        print(f"  {i}. {step}")
    
    enhanced1 = parse_database_switch_commands(example1_steps, "GZ")
    print("\n解析后步骤:")
    for i, step in enumerate(enhanced1, 1):
        if step.startswith("[内部指令]"):
            print(f"  {i}. 🔄 {step}")
        else:
            print(f"  {i}. {step}")
    
    # 示例2：多省份数据汇总
    example2_steps = [
        "查询本省的关键业务指标数据",
        "[切换数据库:JT] 查询全国汇总数据和各省排名",
        "[切换数据库:GZ] 查询贵州省对比数据",
        "[切换数据库:BJ] 查询北京市对比数据",
        "[切换数据库:原始] 汇总所有数据进行综合分析",
        "生成多维度对比分析报告"
    ]
    
    print("\n📊 示例2: 多省份数据汇总分析")
    print("原始步骤:")
    for i, step in enumerate(example2_steps, 1):
        print(f"  {i}. {step}")
    
    enhanced2 = parse_database_switch_commands(example2_steps, "SH")
    print("\n解析后步骤:")
    for i, step in enumerate(enhanced2, 1):
        if step.startswith("[内部指令]"):
            print(f"  {i}. 🔄 {step}")
        else:
            print(f"  {i}. {step}")

def show_usage_guide():
    """显示使用指南"""
    print("\n📋 数据库切换功能使用指南")
    print("=" * 60)
    
    print("🎯 支持的数据库切换语法:")
    print("  [切换数据库:JT] - 切换到公共库（全国数据）")
    print("  [切换数据库:GZ] - 切换到贵州库")
    print("  [切换数据库:BJ] - 切换到北京库")
    print("  [切换数据库:原始] - 切换回原始数据库")
    print("  [切换数据库：XX] - 支持中文冒号")
    
    print("\n🔧 工作原理:")
    print("  1. 系统扫描处理步骤中的 [切换数据库:XX] 命令")
    print("  2. 将切换命令转换为内部指令")
    print("  3. 在执行SQL查询时动态修改数据库配置")
    print("  4. 支持在多个数据库间无缝切换")
    
    print("\n💡 典型使用场景:")
    print("  🔸 省份对比分析:")
    print("    - 查询本省数据")
    print("    - [切换数据库:JT] 查询全国数据")
    print("    - [切换数据库:原始] 对比分析")
    
    print("\n  🔸 多省份汇总:")
    print("    - [切换数据库:GZ] 查询贵州数据")
    print("    - [切换数据库:BJ] 查询北京数据")
    print("    - [切换数据库:JT] 查询全国汇总")
    
    print("\n  🔸 跨区域对比:")
    print("    - 查询本区域数据")
    print("    - [切换数据库:其他省份] 查询对比数据")
    print("    - [切换数据库:JT] 查询全国基准")

if __name__ == "__main__":
    print("🚀 数据库切换功能测试（简化版）")
    print("=" * 60)
    
    # 测试解析功能
    test_database_switch_parsing()

    # 测试SQL判断逻辑
    test_sql_judgment()

    # 测试system消息修改
    test_system_message_modification()
    
    # 测试真实示例
    test_real_world_examples()
    
    # 显示使用指南
    show_usage_guide()
    
    print("\n" + "=" * 60)
    print("🎉 数据库切换功能测试完成！")
    print("\n💡 下一步:")
    print("1. 启动API服务器: python examples/analysis_agent_server.py")
    print("2. 访问管理界面: http://localhost:8001/admin")
    print("3. 测试问题: '本省电费与全国对比如何'")
    print("4. 查看系统如何自动切换数据库执行查询")
