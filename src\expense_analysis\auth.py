"""
费用分析API鉴权模块
"""
from fastapi import HTTPException, Security, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Tuple
import logging
from .auth_config import AuthConfig

# 配置日志
logger = logging.getLogger(__name__)

# 创建Bearer token验证器
security = HTTPBearer(auto_error=False)

def verify_token(credentials: Optional[HTTPAuthorizationCredentials] = Security(security)) -> str:
    """
    验证API Token

    Args:
        credentials: HTTP Bearer token (可选)

    Returns:
        str: 验证通过的token

    Raises:
        HTTPException: token无效时抛出401错误
    """
    logger.info("🔐 [AUTH] 开始验证Token")

    # 如果鉴权被禁用，直接返回
    if not AuthConfig.ENABLE_AUTH:
        logger.info("🔓 [AUTH] 鉴权已禁用，跳过验证")
        return "auth_disabled"

    # 如果启用鉴权但没有提供credentials
    if credentials is None:
        logger.warning("❌ [AUTH] 未提供credentials")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要提供API Token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    token_masked = token[:10] + "..." + token[-6:] if len(token) > 16 else "***"
    logger.info(f"🔑 [AUTH] 验证Token: {token_masked}")

    # 检查token有效性
    valid_tokens = AuthConfig.get_valid_tokens()
    logger.info(f"📊 [AUTH] 当前有效Token数量: {len(valid_tokens)}")

    if not AuthConfig.is_valid_token(token):
        logger.warning(f"❌ [AUTH] Token无效: {token_masked}")
        logger.warning(f"📋 [AUTH] 有效Token列表前3个: {[t[:10]+'...' for t in list(valid_tokens)[:3]]}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的API Token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.info(f"✅ [AUTH] Token验证成功: {token_masked}")
    return token

def verify_token_with_database(credentials: Optional[HTTPAuthorizationCredentials] = Security(security)) -> Tuple[str, str]:
    """
    验证API Token并返回对应的数据库信息

    Args:
        credentials: HTTP Bearer token (可选)

    Returns:
        Tuple[str, str]: (token, database_name)

    Raises:
        HTTPException: token无效时抛出401错误
    """
    logger.info("🗄️ [AUTH] 开始验证Token并获取数据库信息")

    # 先验证token
    token = verify_token(credentials)

    # 如果鉴权被禁用，返回默认数据库
    if not AuthConfig.ENABLE_AUTH:
        logger.info("🔓 [AUTH] 鉴权禁用，使用默认数据库: analysis_gz")
        return token, "analysis_gz"

    # 获取token对应的数据库
    database = AuthConfig.get_database_by_token(token)
    token_masked = token[:10] + "..." + token[-6:] if len(token) > 16 else "***"

    if not database:
        logger.warning(f"❌ [AUTH] Token {token_masked} 未配置对应的数据库")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token未配置对应的数据库",
            headers={"WWW-Authenticate": "Bearer"},
        )

    logger.info(f"✅ [AUTH] Token {token_masked} 对应数据库: {database}")
    return token, database


def get_current_token(credentials: Optional[HTTPAuthorizationCredentials] = Security(security)) -> str:
    """
    获取当前请求的token (用于需要token信息的接口)
    """
    return verify_token(credentials)


# 可选：创建一个装饰器版本
def require_auth(func):
    """
    装饰器：为函数添加鉴权要求
    """
    def wrapper(*args, **kwargs):
        # 这里可以添加额外的鉴权逻辑
        return func(*args, **kwargs)
    return wrapper
