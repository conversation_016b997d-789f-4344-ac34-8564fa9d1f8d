"""
Agent执行器
管理Agent对话历史和执行统计
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from .tool_calling_agent import ToolCallingAgent
from .enhanced_tool_calling_agent import EnhancedToolCallingAgent


class AgentExecutor:
    """Agent执行器，管理对话历史和统计信息"""
    
    def __init__(self, 
                 agent: Union[ToolCallingAgent, EnhancedToolCallingAgent],
                 max_history: int = 100):
        """
        初始化执行器
        
        Args:
            agent: Agent实例（支持基础版和增强版）
            max_history: 最大对话历史条数
        """
        self.agent = agent
        self.max_history = max_history
        self.conversation_history = []
        self.stats = {
            "total_messages": 0,
            "tool_calls": 0,
            "successful_tool_calls": 0,
            "failed_tool_calls": 0,
            "start_time": datetime.now()
        }
        self.tool_usage = {}  # 工具使用统计
    
    def chat(self, message: str) -> Dict[str, Any]:
        """
        与Agent对话
        
        Args:
            message: 用户消息
            
        Returns:
            Agent回复结果
        """
        # 更新统计
        self.stats["total_messages"] += 1
        
        # 处理消息
        result = self.agent.process_message(message, self.conversation_history)
        
        # 添加到对话历史
        self._add_to_history("user", message)
        self._add_to_history("assistant", result["response"])
        
        # 更新工具调用统计
        self._update_tool_stats(result)
        
        return result
    
    def _add_to_history(self, role: str, content: str):
        """添加消息到对话历史"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # 限制历史长度
        if len(self.conversation_history) > self.max_history:
            self.conversation_history = self.conversation_history[-self.max_history:]
    
    def _update_tool_stats(self, result: Dict[str, Any]):
        """更新工具使用统计"""
        if result.get("tool_used"):
            # 处理增强版Agent的多步骤工具调用
            if "tool_calls_history" in result:
                # 增强版Agent
                for tool_call_info in result["tool_calls_history"]:
                    tool_call = tool_call_info["tool_call"]
                    tool_result = tool_call_info["tool_result"]
                    self._update_single_tool_stat(tool_call["tool_name"], tool_result["success"])
            else:
                # 基础版Agent
                tool_call = result.get("tool_call", {})
                tool_result = result.get("tool_result", {})
                if tool_call.get("tool_name"):
                    self._update_single_tool_stat(tool_call["tool_name"], tool_result.get("success", False))
    
    def _update_single_tool_stat(self, tool_name: str, success: bool):
        """更新单个工具的统计"""
        self.stats["tool_calls"] += 1
        
        if success:
            self.stats["successful_tool_calls"] += 1
        else:
            self.stats["failed_tool_calls"] += 1
        
        # 工具使用统计
        if tool_name not in self.tool_usage:
            self.tool_usage[tool_name] = {
                "total_calls": 0,
                "successful_calls": 0,
                "failed_calls": 0
            }
        
        self.tool_usage[tool_name]["total_calls"] += 1
        if success:
            self.tool_usage[tool_name]["successful_calls"] += 1
        else:
            self.tool_usage[tool_name]["failed_calls"] += 1
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_stats = self.stats.copy()
        current_stats["duration_minutes"] = (
            datetime.now() - self.stats["start_time"]
        ).total_seconds() / 60
        
        # 计算成功率
        total_calls = current_stats["tool_calls"]
        if total_calls > 0:
            current_stats["tool_success_rate"] = current_stats["successful_tool_calls"] / total_calls
        else:
            current_stats["tool_success_rate"] = 0.0
        
        return current_stats
    
    def get_tool_usage_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取工具使用摘要"""
        summary = {}
        for tool_name, usage in self.tool_usage.items():
            summary[tool_name] = {
                "total_calls": usage["total_calls"],
                "successful_calls": usage["successful_calls"],
                "failed_calls": usage["failed_calls"],
                "success_rate": usage["successful_calls"] / usage["total_calls"] if usage["total_calls"] > 0 else 0.0
            }
        return summary
    
    def export_conversation(self, filename: Optional[str] = None) -> str:
        """
        导出对话记录
        
        Args:
            filename: 导出文件名，如果为None则自动生成
            
        Returns:
            导出文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_export_{timestamp}.json"
        
        export_data = {
            "export_time": datetime.now().isoformat(),
            "stats": self.get_stats(),
            "tool_usage": self.get_tool_usage_summary(),
            "conversation_history": self.conversation_history,
            "agent_type": type(self.agent).__name__
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def import_conversation(self, filename: str):
        """
        导入对话记录
        
        Args:
            filename: 导入文件路径
        """
        if not os.path.exists(filename):
            raise FileNotFoundError(f"文件不存在: {filename}")
        
        with open(filename, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        # 恢复对话历史
        self.conversation_history = import_data.get("conversation_history", [])
        
        # 恢复统计信息（可选）
        if "stats" in import_data:
            imported_stats = import_data["stats"]
            self.stats.update({
                "total_messages": imported_stats.get("total_messages", 0),
                "tool_calls": imported_stats.get("tool_calls", 0),
                "successful_tool_calls": imported_stats.get("successful_tool_calls", 0),
                "failed_tool_calls": imported_stats.get("failed_tool_calls", 0)
            })
        
        # 恢复工具使用统计（可选）
        if "tool_usage" in import_data:
            for tool_name, usage in import_data["tool_usage"].items():
                self.tool_usage[tool_name] = {
                    "total_calls": usage.get("total_calls", 0),
                    "successful_calls": usage.get("successful_calls", 0),
                    "failed_calls": usage.get("failed_calls", 0)
                }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_messages": 0,
            "tool_calls": 0,
            "successful_tool_calls": 0,
            "failed_tool_calls": 0,
            "start_time": datetime.now()
        }
        self.tool_usage = {} 