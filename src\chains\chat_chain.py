"""
聊天链模块
实现基础的对话功能和RAG（检索增强生成）功能
"""

from typing import List, Dict, Any, Optional
from langchain.chains import ConversationalR<PERSON><PERSON>valChain, LLMChain
from langchain.prompts import PromptTemplate, ChatPromptTemplate, MessagesPlaceholder
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.vectorstores import VectorStore
from langchain_core.documents import Document

from ..core import MemoryManager


class ChatChain:
    """聊天链类"""
    
    def __init__(
        self,
        llm: BaseLanguageModel,
        memory_type: str = "buffer",
        session_id: str = "default",
        storage_type: str = "memory"
    ):
        """
        初始化聊天链
        
        Args:
            llm: 语言模型
            memory_type: 记忆类型
            session_id: 会话ID
            storage_type: 存储类型
        """
        self.llm = llm
        self.memory = MemoryManager.create_memory(
            memory_type=memory_type,
            session_id=session_id,
            storage_type=storage_type,
            llm=llm
        )
        
        # 创建聊天提示模板
        self.chat_prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个有用的AI助手。请用中文回答用户的问题。"),
            MessagesPlaceholder(variable_name="history"),
            ("human", "{input}")
        ])
        
        # 创建聊天链
        self.chain = LLMChain(
            llm=self.llm,
            prompt=self.chat_prompt,
            memory=self.memory,
            verbose=True
        )
    
    def chat(self, message: str) -> str:
        """
        进行对话
        
        Args:
            message: 用户消息
            
        Returns:
            str: AI回复
        """
        try:
            response = self.chain.invoke({"input": message})
            return response["text"]
        except Exception as e:
            return f"抱歉，处理您的请求时出现错误：{str(e)}"
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取记忆信息"""
        return MemoryManager.get_memory_info(self.memory)
    
    def clear_memory(self):
        """清空记忆"""
        self.memory.clear()


class RAGChain:
    """RAG（检索增强生成）链类"""
    
    def __init__(
        self,
        llm: BaseLanguageModel,
        vectorstore: VectorStore,
        memory_type: str = "buffer",
        session_id: str = "default",
        storage_type: str = "memory"
    ):
        """
        初始化RAG链
        
        Args:
            llm: 语言模型
            vectorstore: 向量存储
            memory_type: 记忆类型
            session_id: 会话ID
            storage_type: 存储类型
        """
        self.llm = llm
        self.vectorstore = vectorstore
        self.memory = MemoryManager.create_memory(
            memory_type=memory_type,
            session_id=session_id,
            storage_type=storage_type,
            llm=llm
        )
        
        # 创建检索器
        self.retriever = vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 3}
        )
        
        # 创建RAG链
        self.chain = ConversationalRetrievalChain.from_llm(
            llm=self.llm,
            retriever=self.retriever,
            memory=self.memory,
            return_source_documents=True,
            verbose=True
        )
    
    def chat(self, question: str) -> Dict[str, Any]:
        """
        进行RAG对话
        
        Args:
            question: 用户问题
            
        Returns:
            Dict[str, Any]: 包含答案和源文档的字典
        """
        try:
            result = self.chain.invoke({"question": question})
            return {
                "answer": result["answer"],
                "source_documents": [
                    {
                        "content": doc.page_content,
                        "metadata": doc.metadata
                    }
                    for doc in result.get("source_documents", [])
                ]
            }
        except Exception as e:
            return {
                "answer": f"抱歉，处理您的请求时出现错误：{str(e)}",
                "source_documents": []
            }
    
    def add_documents(self, documents: List[Document]):
        """
        添加文档到向量存储
        
        Args:
            documents: 文档列表
        """
        self.vectorstore.add_documents(documents)
    
    def search_similar_documents(self, query: str, k: int = 5) -> List[Document]:
        """
        搜索相似文档
        
        Args:
            query: 查询文本
            k: 返回文档数量
            
        Returns:
            List[Document]: 相似文档列表
        """
        return self.vectorstore.similarity_search(query, k=k)
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取记忆信息"""
        return MemoryManager.get_memory_info(self.memory)
    
    def clear_memory(self):
        """清空记忆"""
        self.memory.clear() 