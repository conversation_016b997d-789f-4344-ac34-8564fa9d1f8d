2025-08-18 10:23:32,154 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:23:32,156 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 10:23:32,157 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 10:23:32,158 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 10:23:32,163 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 10:23:33,347 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:23:33,798 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:23:33,800 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:23:33,801 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:27:30,638 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:27:30,640 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,641 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:27:30,642 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:27:30,643 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:27:30,644 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:27:30,963 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:27:31,443 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:27:31,596 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-18 10:27:31,598 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-18 10:27:31,598 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-18 10:31:35,811 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:31:35,813 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:31:35,813 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:31:35,814 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:31:35,815 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:31:35,816 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:31:36,109 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:31:36,150 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:31:37,148 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 10:31:37,148 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 10:31:37,177 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 10:31:37,178 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 10:31:37,178 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 10:31:37,179 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 10:31:37,179 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 10:31:37,180 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:31:37,181 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:31:37,182 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 10:31:37,182 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 10:31:37,392 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:31:37,393 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:31:37,393 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:37:43,135 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:37:43,137 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 10:37:43,138 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:37:43,138 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 10:37:43,140 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 10:37:43,140 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 10:37:43,141 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 10:37:43,141 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 10:37:43,473 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 10:37:43,913 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 10:37:44,190 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 10:37:44,191 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 10:37:44,207 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 10:37:44,207 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 10:37:44,208 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 10:37:44,209 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 10:37:44,209 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 10:37:44,210 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:37:44,210 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 10:37:44,211 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 10:37:44,211 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 10:37:44,255 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 10:37:44,256 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 10:37:44,257 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:11:40,043 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:11:40,044 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:11:40,044 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:11:40,045 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:11:40,047 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:11:40,047 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:11:40,048 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:11:40,048 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:11:40,695 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 11:11:41,553 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:11:42,305 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:11:42,306 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:11:42,324 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:11:42,325 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:11:42,326 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:11:42,327 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:11:42,327 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:11:42,328 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:11:42,328 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:11:42,453 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:11:42,454 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:11:42,455 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:12:16,107 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:12:16,109 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:12:16,109 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:12:16,110 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:12:16,111 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:12:16,112 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:12:16,112 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:12:16,113 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:12:16,412 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 11:12:16,850 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:12:17,167 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:12:17,168 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:12:17,185 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:12:17,185 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:12:17,186 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:12:17,186 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:12:17,187 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:12:17,187 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:12:17,188 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:12:17,189 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:12:17,189 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:12:17,241 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:12:17,242 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:12:17,242 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:47:37,537 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:47:37,539 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 11:47:37,539 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:37,540 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:37,541 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 11:47:37,542 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 11:47:38,862 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,863 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,863 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 11:47:38,864 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 11:47:39,339 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 11:47:39,630 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 11:47:39,631 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 11:47:39,648 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 11:47:39,649 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 11:47:39,650 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 11:47:39,650 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 11:47:39,651 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 11:47:39,651 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:47:39,652 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 11:47:39,652 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 11:47:39,653 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 11:47:39,712 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 11:47:39,713 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 11:47:39,713 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:05,498 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:05,501 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:35:05,502 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:05,502 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:35:05,503 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:35:05,504 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:05,505 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:35:05,505 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:35:06,969 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,970 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,970 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:06,971 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:35:07,425 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:35:07,703 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:35:07,703 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:35:07,721 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:35:07,721 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:35:07,722 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:35:07,722 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:35:07,723 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:35:07,723 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:07,724 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:07,725 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:35:07,725 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 12:35:07,774 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:07,775 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:07,776 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:40,262 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:40,264 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:35:40,264 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:40,265 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:35:40,266 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:35:40,266 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:40,267 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:35:40,267 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:35:41,659 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,660 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,660 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:35:41,661 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:35:42,157 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:35:42,421 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:35:42,421 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:35:42,439 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:35:42,440 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:35:42,440 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:35:42,441 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:35:42,441 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:35:42,442 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:42,443 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:35:42,443 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:35:42,444 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 12:35:42,495 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:35:42,495 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:35:42,496 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:39:08,709 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:39:08,711 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 12:39:08,711 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:08,711 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 12:39:08,712 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 12:39:08,713 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:08,713 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 12:39:08,713 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 12:39:10,052 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:10,054 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:10,055 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 12:39:10,055 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 12:39:10,626 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:39:10,909 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:39:10,910 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:39:10,930 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:39:10,931 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:39:10,931 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:39:10,932 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:39:10,933 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:39:10,933 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:39:10,934 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:39:10,935 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:39:10,937 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 12:39:10,998 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:39:10,999 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:39:11,000 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:48:54,910 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:48:54,912 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 12:48:54,913 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 12:48:54,913 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 12:48:54,914 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 12:48:54,914 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 12:48:54,915 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 12:48:55,445 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:48:55,727 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:48:55,728 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:48:55,747 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:48:55,748 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:48:55,749 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:48:55,750 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:48:55,751 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:48:55,752 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:48:55,752 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:48:55,753 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:48:55,754 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 12:48:55,809 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:48:55,809 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:48:55,810 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:56:37,691 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:56:37,693 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 12:56:37,694 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 12:56:37,694 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 12:56:37,695 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 12:56:37,695 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 12:56:37,696 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 12:56:38,212 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 12:56:38,526 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 12:56:38,526 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 12:56:38,547 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 12:56:38,547 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 12:56:38,548 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 12:56:38,548 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 12:56:38,549 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 12:56:38,550 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:56:38,551 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 12:56:38,552 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 12:56:38,555 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 12:56:38,617 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 12:56:38,619 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 12:56:38,621 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:41:27,565 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:41:27,567 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 13:41:27,568 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:41:27,568 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 13:41:27,572 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 13:41:27,573 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:41:27,573 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 13:41:27,574 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 13:41:27,879 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 13:41:28,327 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 13:41:28,621 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 13:41:28,621 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 13:41:28,640 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 13:41:28,640 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 13:41:28,641 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 13:41:28,642 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 13:41:28,643 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 13:41:28,643 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:41:28,644 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:41:28,645 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 13:41:28,645 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 13:41:28,701 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:41:28,702 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:41:28,702 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:44:46,623 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:44:46,625 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 未配置embedding模型，使用占位符embedding
2025-08-18 13:44:46,626 - src.core.prompt_vectorstore_manager - INFO - 💡 请在.env中配置:
2025-08-18 13:44:46,626 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_ENABLED=true
2025-08-18 13:44:46,627 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_URL=您的embedding服务地址
2025-08-18 13:44:46,627 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_API_KEY=您的API密钥
2025-08-18 13:44:46,628 - src.core.prompt_vectorstore_manager - INFO -    REMOTE_EMBEDDING_MODEL=您的模型名称
2025-08-18 13:44:47,138 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 13:44:47,415 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 13:44:47,416 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 13:44:47,440 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 13:44:47,442 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 13:44:47,443 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 13:44:47,443 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 13:44:47,444 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 13:44:47,444 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:44:47,445 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:44:47,445 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 13:44:47,446 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 13:44:47,509 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:44:47,510 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:44:47,511 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:48:29,875 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:48:29,877 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 13:48:29,877 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:29,877 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 13:48:29,879 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 13:48:29,880 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:29,881 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 13:48:29,881 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 13:48:31,767 - src.core.remote_embeddings - ERROR - ❌ 远程embedding请求失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:31,767 - src.core.remote_embeddings - ERROR - ❌ 查询向量化失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:31,768 - src.core.remote_embeddings - ERROR - ❌ 远程embedding服务连接失败: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 13:48:31,769 - src.core.prompt_vectorstore_manager - WARNING - ⚠️ 远程embedding服务连接测试失败，但继续初始化
2025-08-18 13:48:32,231 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 13:48:32,510 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 13:48:32,510 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 13:48:32,527 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 13:48:32,528 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 13:48:32,528 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 13:48:32,529 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 13:48:32,529 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 13:48:32,530 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:48:32,530 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:48:32,531 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 13:48:32,532 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-18 13:48:32,584 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 13:48:32,584 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 13:48:32,585 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 13:53:35,052 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:53:49,268 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:54:06,812 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:54:26,557 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:58:07,134 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:58:23,094 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: 502 Server Error: Bad Gateway for url: http://proxy.ai.iot.chinamobile.com/imaas/v1/chat/completions
2025-08-18 13:59:04,478 - langsmith.client - WARNING - Failed to multipart ingest runs: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=c64b0e97-5020-4b38-ac01-b43d949b5676,id=c64b0e97-5020-4b38-ac01-b43d949b5676
2025-08-18 13:59:13,331 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: HTTPConnectionPool(host='127.0.0.1', port=33210): Read timed out. (read timeout=10)
2025-08-18 13:59:13,692 - langsmith.client - WARNING - Failed to send compressed multipart ingest: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=c64b0e97-5020-4b38-ac01-b43d949b5676,id=c64b0e97-5020-4b38-ac01-b43d949b5676
2025-08-18 14:00:00,356 - langsmith.client - WARNING - Failed to multipart ingest runs: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=90f417e4-7a57-42fc-bd14-58cf4bef31c5,id=90f417e4-7a57-42fc-bd14-58cf4bef31c5
2025-08-18 14:00:09,686 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: HTTPConnectionPool(host='ai.ai.iot.chinamobile.com', port=80): Read timed out. (read timeout=10)
2025-08-18 14:00:09,997 - langsmith.client - WARNING - Failed to send compressed multipart ingest: langsmith.utils.LangSmithAuthError: Authentication failed for https://api.smith.langchain.com/runs/multipart. HTTPError('401 Client Error: Unauthorized for url: https://api.smith.langchain.com/runs/multipart', '{"error":"Unauthorized"}\n')trace=90f417e4-7a57-42fc-bd14-58cf4bef31c5,id=90f417e4-7a57-42fc-bd14-58cf4bef31c5
2025-08-18 14:02:14,312 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:02:14,313 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 14:02:14,313 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:02:14,314 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 14:02:14,315 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 14:02:14,316 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:02:14,316 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 14:02:14,317 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 14:02:14,641 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 14:02:15,145 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 14:02:15,396 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 14:02:15,396 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 14:02:15,416 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 14:02:15,417 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 14:02:15,418 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 14:02:15,418 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 14:02:15,419 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 14:02:15,419 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:02:15,420 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 14:02:15,420 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 14:02:15,421 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 14:02:15,476 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:02:15,477 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:02:15,478 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 14:03:20,558 - __main__ - ERROR - ❌ 服务启动失败: LLM服务不可用: API request failed: HTTPConnectionPool(host='proxy.ai.iot.chinamobile.com', port=80): Max retries exceeded with url: /imaas/v1/chat/completions (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x000002692DA78460>: Failed to resolve 'proxy.ai.iot.chinamobile.com' ([Errno 11001] getaddrinfo failed)"))
2025-08-18 14:03:43,729 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:03:43,730 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 14:03:43,731 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:03:43,731 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 14:03:43,732 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 14:03:43,733 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 14:03:43,733 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 14:03:43,733 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 14:03:43,996 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 14:03:44,445 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-18 14:03:44,700 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-18 14:03:44,700 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-18 14:03:44,719 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-18 14:03:44,719 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-18 14:03:44,720 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-18 14:03:44,720 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-18 14:03:44,721 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-18 14:03:44,721 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:03:44,722 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 14:03:44,723 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-18 14:03:44,723 - __main__ - INFO - 🌐 监听地址: 127.0.0.1:8001
2025-08-18 14:03:44,770 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 14:03:44,771 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-18 14:03:44,771 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-18 17:15:19,182 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:15:19,182 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:15:19,183 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:19,183 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:15:19,185 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:15:19,185 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:19,186 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:15:19,186 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:15:19,499 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:15:20,322 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:20,323 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:20,326 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:49,982 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:15:49,983 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:15:49,984 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:49,984 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:15:49,986 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:15:49,986 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:15:49,987 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:15:49,987 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:15:50,277 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:15:50,672 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:50,673 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:15:50,675 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:16:06,302 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:16:06,302 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:16:06,303 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:16:06,303 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:16:06,304 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:16:06,305 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:16:06,305 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:16:06,306 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:16:06,616 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:16:07,051 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:16:07,052 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:16:07,054 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:19:07,021 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:19:07,022 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:19:07,023 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:19:07,023 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:19:07,027 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:19:07,028 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:19:07,028 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:19:07,029 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:19:07,353 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:19:07,728 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:19:07,729 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:19:07,732 - __main__ - ERROR - ❌ 服务启动失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:30:26,331 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:30:26,333 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:30:26,334 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:30:26,334 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:30:26,336 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:30:26,336 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:30:26,337 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:30:26,337 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:30:26,629 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:30:27,008 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:30:27,009 - __main__ - ERROR - ❌ SQL Agent初始化失败: Descriptors cannot not be created directly.
If this call came from a _pb2.py file, your generated code is out of date and must be regenerated with protoc >= 3.19.0.
If you cannot immediately regenerate your protos, some other possible workarounds are:
 1. Downgrade the protobuf package to 3.20.x or lower.
 2. Set PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python (but this will use pure-Python parsing and will be much slower).

More information: https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates
2025-08-18 17:34:51,873 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-18 17:34:51,876 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-18 17:34:51,876 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:34:51,877 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-18 17:34:51,878 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-18 17:34:51,879 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-18 17:34:51,879 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-18 17:34:51,880 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-18 17:34:52,187 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-18 17:34:52,524 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: Could not import chromadb python package. Please install it with `pip install chromadb`.
2025-08-18 17:34:52,525 - __main__ - ERROR - ❌ SQL Agent初始化失败: Could not import chromadb python package. Please install it with `pip install chromadb`.
