"""
SQL工作流示例
演示如何使用Agent进行完整的SQL生成和执行工作流
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory
from src.agents import ToolCallingAgent, AgentExecutor
from src.tools import get_all_tools
from src.config import settings


def display_sql_tools():
    """显示SQL相关工具"""
    print("=== SQL相关工具 ===")
    tools = get_all_tools()
    sql_tools = [tool for tool in tools if 'sql' in tool.name.lower()]
    
    for i, tool in enumerate(sql_tools, 1):
        print(f"{i}. {tool.name}: {tool.description}")
    print()


def test_sql_workflow():
    """测试SQL工作流"""
    print("=== SQL工作流测试 ===")
    
    try:
        # 创建千问模型
        print("正在初始化千问模型...")
        llm = LLMFactory.create_chat_model(
            provider="local",
            temperature=0.7
        )
        print("✅ 千问模型初始化成功!")
        
        # 创建Agent
        agent = ToolCallingAgent(llm=llm)
        executor = AgentExecutor(agent=agent)
        
        print("✅ Agent初始化成功!")
        print(f"可用工具数量: {len(agent.tools)}")
        
        # SQL工作流测试用例
        sql_queries = [
            "帮我生成一个查询2025年4月电费的SQL语句",
            "执行这个SQL: SELECT * FROM analysis_reference_ele WHERE year = 2025 AND month = 4",
            "查询2025年所有月份的平均电费",
            "查询活跃用户的信息",
            "统计2024年和2025年的电费总额对比",
        ]
        
        for i, query in enumerate(sql_queries, 1):
            print(f"\n--- SQL工作流测试 {i}: {query} ---")
            result = executor.chat(query)
            
            print(f"回复: {result['response']}")
            if result['tool_used']:
                print(f"使用工具: {result['tool_call']['tool_name']}")
                print(f"工具结果: {result['tool_result']['success']}")
                
                # 如果是SQL执行工具，显示详细结果
                if result['tool_call']['tool_name'] == 'sql_executor':
                    tool_result = result['tool_result']
                    if tool_result['success'] and tool_result['result']:
                        data = tool_result['result']
                        print(f"  返回行数: {data['row_count']}")
                        if data['rows']:
                            print("  数据预览:")
                            for j, row in enumerate(data['rows'][:3]):
                                print(f"    第{j+1}行: {row}")
            print()
        
        # 显示统计信息
        stats = executor.get_stats()
        tool_usage = executor.get_tool_usage_summary()
        
        print("=== 执行统计 ===")
        print(f"总消息数: {stats['total_messages']}")
        print(f"工具调用次数: {stats['tool_calls']}")
        print(f"成功率: {stats['tool_success_rate']:.1%}")
        
        if tool_usage:
            print("\n工具使用详情:")
            for tool_name, usage in tool_usage.items():
                print(f"  {tool_name}: {usage['successful_calls']}/{usage['total_calls']} 成功")
        
        return executor
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None


def interactive_sql_chat():
    """交互式SQL聊天"""
    print("\n=== 交互式SQL聊天模式 ===")
    
    try:
        # 创建千问模型和Agent
        llm = LLMFactory.create_chat_model(provider="local", temperature=0.7)
        agent = ToolCallingAgent(llm=llm)
        executor = AgentExecutor(agent=agent)
        
        print("🤖 SQL智能Agent已启动!")
        print("💡 我可以帮您:")
        print("   - 生成SQL查询语句")
        print("   - 执行SQL查询并返回结果")
        print("   - 解释SQL语句的含义")
        print("   - 进行数据库查询和分析")
        
        print("\n💡 示例问题:")
        print("   - '生成查询2025年4月电费的SQL'")
        print("   - '执行SQL: SELECT * FROM users'")
        print("   - '查询活跃用户数量'")
        print("   - '统计各年份电费总额'")
        
        print("\n💡 特殊命令:")
        print("   - 'quit' / 'exit' / 'q': 退出")
        print("   - 'clear': 清空对话历史")
        print("   - 'stats': 查看统计信息")
        print("   - 'tables': 查看示例数据表结构")
        print("-" * 60)
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                elif user_input.lower() == 'clear':
                    executor.clear_history()
                    print("🗑️ 对话历史已清空")
                    continue
                elif user_input.lower() == 'stats':
                    stats = executor.get_stats()
                    tool_usage = executor.get_tool_usage_summary()
                    print("\n📊 统计信息:")
                    print(f"   总消息数: {stats['total_messages']}")
                    print(f"   工具调用次数: {stats['tool_calls']}")
                    print(f"   成功率: {stats['tool_success_rate']:.1%}")
                    if tool_usage:
                        print("   工具使用详情:")
                        for tool_name, usage in tool_usage.items():
                            print(f"     {tool_name}: {usage['successful_calls']}/{usage['total_calls']} 成功")
                    continue
                elif user_input.lower() == 'tables':
                    print("\n📋 示例数据表结构:")
                    print("1. electricity_bills (电费表):")
                    print("   - id: 主键")
                    print("   - year: 年份") 
                    print("   - month: 月份")
                    print("   - amount: 金额")
                    print("   - description: 描述")
                    print("   - created_date: 创建日期")
                    print("\n2. users (用户表):")
                    print("   - id: 主键")
                    print("   - name: 姓名")
                    print("   - email: 邮箱")
                    print("   - registration_date: 注册日期")
                    print("   - status: 状态 (active/inactive)")
                    continue
                elif not user_input:
                    print("⚠️ 请输入有效内容")
                    continue
                
                # 处理用户消息
                print("🤖 正在分析您的需求...", end="", flush=True)
                result = executor.chat(user_input)
                print("\r" + " " * 30 + "\r", end="")  # 清除"正在分析..."
                
                print(f"🤖 Agent: {result['response']}")
                
                # 显示工具使用信息
                if result['tool_used']:
                    tool_call = result['tool_call']
                    tool_result = result['tool_result']
                    print(f"   🔧 使用了工具: {tool_call['tool_name']}")
                    
                    if tool_result['success']:
                        print(f"   ✅ 工具执行成功")
                        
                        # 特殊处理SQL执行结果
                        if tool_call['tool_name'] == 'sql_executor' and tool_result['result']:
                            data = tool_result['result']
                            print(f"   📊 查询结果: {data['row_count']} 行")
                            if data['rows'] and len(data['rows']) <= 10:
                                print("   📋 详细数据:")
                                for j, row in enumerate(data['rows']):
                                    print(f"     第{j+1}行: {row}")
                            elif data['rows']:
                                print("   📋 数据预览 (前5行):")
                                for j, row in enumerate(data['rows'][:5]):
                                    print(f"     第{j+1}行: {row}")
                                print(f"     ... 还有{len(data['rows'])-5}行")
                    else:
                        print(f"   ❌ 工具执行失败: {tool_result.get('error', '未知错误')}")
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                continue
                
    except Exception as e:
        print(f"❌ 初始化失败: {e}")


def show_config():
    """显示当前配置"""
    print("=== 当前配置信息 ===")
    print(f"本地LLM启用: {settings.local_llm_enabled}")
    if settings.local_llm_enabled:
        print(f"  模型URL: {settings.local_llm_model_url}")
        print(f"  模型名称: {settings.local_llm_model_name}")
        print(f"  API密钥: {'已设置' if settings.local_llm_api_key else '未设置'}")
    else:
        print("⚠️ 本地LLM未启用，请在.env文件中配置")
    print()


def main():
    """主函数"""
    print("=== SQL工作流Agent示例 ===\n")
    
    # 显示配置
    show_config()
    
    # 显示SQL工具
    display_sql_tools()
    
    # 检查配置
    if not settings.local_llm_enabled:
        print("❌ 请先在.env文件中启用和配置千问模型:")
        print("   LOCAL_LLM_ENABLED=true")
        print("   LOCAL_LLM_API_KEY=your_api_key")
        print("   LOCAL_LLM_MODEL_URL=your_model_url")
        print("   LOCAL_LLM_MODEL_NAME=your_model_name")
        return
    
    # 选择运行模式
    print("请选择运行模式:")
    print("1. SQL工作流测试")
    print("2. 交互式SQL聊天")
    print("3. 退出")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        executor = test_sql_workflow()
        if executor:
            # 询问是否要继续交互式聊天
            continue_chat = input("\n是否要继续交互式SQL聊天? (y/n): ").strip().lower()
            if continue_chat in ['y', 'yes']:
                print("\n=== 切换到交互式SQL聊天模式 ===")
                print("🤖 继续使用当前Agent进行SQL交互...")
                interactive_sql_chat()
    
    elif choice == "2":
        interactive_sql_chat()
    
    elif choice == "3":
        print("👋 再见!")
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main() 