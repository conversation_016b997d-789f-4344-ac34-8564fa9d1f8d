#!/usr/bin/env python3
"""
测试标准化语法功能
"""

import sys
import os
import re

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def map_tool_name(tool_name):
    """工具名称映射"""
    tool_mapping = {
        # 知识搜索工具的各种别名
        "知识": "knowledge_search",
        "搜索": "knowledge_search",
        "问答": "knowledge_search",
        "查询": "knowledge_search",
        "kb": "knowledge_search",
        "ask": "knowledge_search",
        "search": "knowledge_search",
        "知识搜索": "knowledge_search",
        "knowledge": "knowledge_search",

        # SQL工具的别名
        "sql": "integrated_sql",
        "数据库": "integrated_sql",
        "SQL": "integrated_sql",
    }

    mapped_name = tool_mapping.get(tool_name.lower(), tool_name)
    if mapped_name != tool_name:
        print(f"    🔄 [工具映射] {tool_name} → {mapped_name}")
    return mapped_name

def parse_single_step(step, original_province_code="GZ"):
    """
    解析单个步骤中的标准化命令（简化版本）
    """
    result_steps = []
    
    print(f"🔍 解析步骤: {step}")
    
    # 1. SQL查询命令: [SQL查询:数据库:查询内容]
    sql_pattern = r'\[SQL查询[:：]([^:：\]]+)[:：]([^\]]+)\]'
    sql_match = re.search(sql_pattern, step)
    if sql_match:
        target_db = sql_match.group(1).strip()
        query_content = sql_match.group(2).strip()
        
        print(f"  🔍 [SQL查询] 数据库={target_db}, 查询={query_content}")
        
        result_steps.append(f"[内部指令-SQL] 切换到数据库: {target_db}")
        result_steps.append(f"调用integrated_sql工具，查询: {query_content}")
        
        # 移除命令，保留其余内容
        clean_step = re.sub(sql_pattern, '', step).strip()
        if clean_step:
            result_steps.append(clean_step)
        
        return result_steps
    
    # 2. 数据库切换命令: [切换数据库:数据库]
    switch_pattern = r'\[切换数据库[:：]([^\]]+)\]'
    switch_match = re.search(switch_pattern, step)
    if switch_match:
        target_db = switch_match.group(1).strip()
        
        print(f"  🔄 [数据库切换] 目标数据库={target_db}")
        
        if target_db.upper() == "原始" or target_db.upper() == "ORIGINAL":
            result_steps.append(f"[内部指令-切换] 切换到原始数据库: {original_province_code}")
        else:
            result_steps.append(f"[内部指令-切换] 切换到数据库: {target_db}")
        
        # 移除命令，保留其余内容
        clean_step = re.sub(switch_pattern, '', step).strip()
        if clean_step:
            result_steps.append(clean_step)
        
        return result_steps
    
    # 3. 工具调用命令: [调用工具:工具名:参数]
    tool_pattern = r'\[调用工具[:：]([^:：\]]+)[:：]([^\]]+)\]'
    tool_match = re.search(tool_pattern, step)
    if tool_match:
        tool_name = tool_match.group(1).strip()
        tool_params = tool_match.group(2).strip()

        # 工具名称映射
        actual_tool_name = map_tool_name(tool_name)

        print(f"  🔧 [工具调用] 工具={tool_name} → {actual_tool_name}, 参数={tool_params}")

        result_steps.append(f"[内部指令-工具] 调用{actual_tool_name}工具，参数: {tool_params}")
        
        # 移除命令，保留其余内容
        clean_step = re.sub(tool_pattern, '', step).strip()
        if clean_step:
            result_steps.append(clean_step)
        
        return result_steps
    
    # 4. 计算命令: [计算:计算逻辑]
    calc_pattern = r'\[计算[:：]([^\]]+)\]'
    calc_match = re.search(calc_pattern, step)
    if calc_match:
        calc_logic = calc_match.group(1).strip()
        
        print(f"  🧮 [计算] 计算逻辑={calc_logic}")
        
        result_steps.append(f"[内部指令-计算] 执行计算: {calc_logic}")
        
        # 移除命令，保留其余内容
        clean_step = re.sub(calc_pattern, '', step).strip()
        if clean_step:
            result_steps.append(clean_step)
        
        return result_steps
    
    # 5. 变量设置命令: [变量:变量名=值]
    var_pattern = r'\[变量[:：]([^\]]+)\]'
    var_match = re.search(var_pattern, step)
    if var_match:
        var_definition = var_match.group(1).strip()
        
        print(f"  📝 [变量] 变量定义={var_definition}")
        
        result_steps.append(f"[内部指令-变量] 设置变量: {var_definition}")
        
        # 移除命令，保留其余内容
        clean_step = re.sub(var_pattern, '', step).strip()
        if clean_step:
            result_steps.append(clean_step)
        
        return result_steps
    
    # 6. 普通步骤
    print(f"  📄 [普通步骤] 无特殊命令")
    result_steps.append(step)
    return result_steps

def test_tool_name_mapping():
    """测试工具名称映射"""
    print("🧪 测试工具名称映射")
    print("=" * 60)

    # 测试各种工具名称别名
    test_tools = [
        "[调用工具:知识:劣化指标改进建议]",
        "[调用工具:搜索:电费优化方案]",
        "[调用工具:问答:如何降低成本]",
        "[调用工具:kb:最佳实践]",
        "[调用工具:ask:改进建议]",
        "[调用工具:knowledge_search:原始名称]",
        "[调用工具:SQL:查询数据]",
        "[调用工具:数据库:获取信息]",
    ]

    print("📝 工具名称映射测试:")
    for tool_step in test_tools:
        print(f"\n🔧 测试: {tool_step}")
        enhanced_steps = parse_single_step(tool_step, "GZ")
        for step in enhanced_steps:
            if step.startswith("[内部指令-工具]"):
                print(f"  结果: {step}")

def test_standardized_syntax():
    """测试标准化语法解析"""
    print("\n🧪 测试标准化语法解析")
    print("=" * 60)
    
    # 测试各种标准化命令
    test_steps = [
        "[SQL查询:JT:当前电费情况如何] 获取全国电费数据",
        "[切换数据库:原始] 切换回原始数据库进行分析",
        "[调用工具:knowledge_search:劣化指标改进建议] 查询改进方案",
        "[计算:本省电费与全国平均的差异百分比] 计算差异指标",
        "[变量:查询结果=步骤1结果] 保存查询数据",
        "普通的分析步骤，没有特殊命令",
        "[SQL查询：GZ：关键业务指标] 测试中文冒号",
    ]
    
    for i, step in enumerate(test_steps, 1):
        print(f"\n📝 测试步骤 {i}:")
        enhanced_steps = parse_single_step(step, "GZ")
        
        print(f"  解析结果:")
        for j, enhanced_step in enumerate(enhanced_steps, 1):
            if enhanced_step.startswith("[内部指令"):
                print(f"    {j}. 🔧 {enhanced_step}")
            else:
                print(f"    {j}. {enhanced_step}")

def test_complex_example():
    """测试复杂示例（您的实际需求）"""
    print("\n🧪 测试复杂示例（实际需求）")
    print("=" * 60)
    
    complex_step = """[SQL查询:JT:当前电费情况如何] [变量:查询结果=SQL结果] [计算:根据查询结果中的指标环比变动情况与全国排名综合赋值并打分，指标环比变动情况对应权重70%，全国排名对应权重30%，整体得分50分以下即为劣化。打分标准如下:指标值环比下降每1%，则加1分,上升1%,则减1分。打分时，环比如果有小数，需要四舍五入，比如0.7%算1%。排名每上升一名,则加5分，,下降一名，则减5分;例如：指标A的5月环比值上升了3%，全国排名下降2名。则指标A的环比分为50-3=47，全国排名分为50-10=40。整体得分为47*70%+40*30%=44.9，得分小于50，需输出改进建议。如果所有指标均未劣化,则固定针对全国排名最低的指标，为劣化] [调用工具:knowledge_search:劣化指标的改进建议] 总结数据"""
    
    print("📝 复杂步骤:")
    print(f"  {complex_step}")
    
    print(f"\n🔍 解析过程:")
    enhanced_steps = parse_single_step(complex_step, "GZ")
    
    print(f"\n📊 解析结果:")
    for i, step in enumerate(enhanced_steps, 1):
        if step.startswith("[内部指令"):
            print(f"  {i}. 🔧 {step}")
        else:
            print(f"  {i}. {step}")

def test_your_original_example():
    """测试您原始的复杂需求"""
    print("\n🧪 测试您的原始复杂需求")
    print("=" * 60)
    
    # 将您的原始需求转换为标准化语法
    original_steps = [
        """调用integrated_sql工具，切换数据库JT，传入"当前电费情况如何" """,
        """根据查询结果中的指标环比变动情况与全国排名综合赋值并打分，指标环比变动情况对应权重70%，全国排名对应权重30%，整体得分50分以下即为劣化。打分标准如下:指标值环比下降每1%，则加1分,上升1%,则减1分。打分时，环比如果有小数，需要四舍五入，比如0.7%算1%。排名每上升一名,则加5分，,下降一名，则减5分;例如：指标A的5月环比值上升了3%，全国排名下降2名。则指标A的环比分为50-3=47，全国排名分为50-10=40。整体得分为47*70%+40*30%=44.9，得分小于50，需输出改进建议。如果所有指标均未劣化,则固定针对全国排名最低的指标，为劣化。""",
        """调用knowledge_search工具，查询劣化指标的改进建议""",
        """总结数据"""
    ]
    
    standardized_steps = [
        """[SQL查询:JT:当前电费情况如何] 获取全国电费数据和排名""",
        """[变量:查询结果=步骤1结果] 保存查询数据""",
        """[计算:综合评分逻辑] 根据查询结果中的指标环比变动情况与全国排名综合赋值并打分：
        - 指标环比变动情况权重70%，全国排名权重30%
        - 整体得分50分以下即为劣化
        - 打分标准：指标值环比下降每1%加1分，上升1%减1分
        - 环比小数四舍五入（如0.7%算1%）
        - 排名每上升一名加5分，下降一名减5分
        - 示例：指标A环比上升3%，排名下降2名
          环比分=50-3=47，排名分=50-10=40
          整体得分=47×70%+40×30%=44.9
          得分<50需输出改进建议""",
        """[计算:劣化指标识别] 如果所有指标均未劣化，则固定针对全国排名最低的指标标记为劣化""",
        """[调用工具:knowledge_search:劣化指标的改进建议] 查询改进方案""",
        """总结数据并生成完整的评分分析报告"""
    ]
    
    print("📝 原始写法 vs 标准化写法对比:")
    
    for i, (original, standardized) in enumerate(zip(original_steps, standardized_steps), 1):
        print(f"\n步骤 {i}:")
        print(f"  原始: {original[:100]}...")
        print(f"  标准: {standardized[:100]}...")
        
        # 解析标准化版本
        enhanced = parse_single_step(standardized, "GZ")
        print(f"  解析: {len(enhanced)} 个内部指令")

def show_syntax_guide():
    """显示语法指南"""
    print("\n📋 标准化语法指南")
    print("=" * 60)
    
    print("🎯 支持的标准化命令:")
    print("1. [SQL查询:数据库:查询内容] - 切换数据库并执行SQL查询")
    print("   示例: [SQL查询:JT:当前电费情况如何]")
    
    print("\n2. [切换数据库:数据库] - 只切换数据库，不执行查询")
    print("   示例: [切换数据库:原始] 或 [切换数据库:GZ]")
    
    print("\n3. [调用工具:工具名:参数] - 调用指定工具")
    print("   示例: [调用工具:知识:劣化指标改进建议]")
    print("   别名: 知识/搜索/问答/kb/ask → knowledge_search")
    
    print("\n4. [计算:计算逻辑] - 执行复杂计算")
    print("   示例: [计算:本省电费与全国平均的差异百分比]")
    
    print("\n5. [变量:变量名=值] - 设置和保存变量")
    print("   示例: [变量:查询结果=步骤1结果]")
    
    print("\n💡 优势:")
    print("- ✅ 语法明确，减少大模型理解错误")
    print("- ✅ 操作类型清晰，避免歧义")
    print("- ✅ 支持复杂的数据传递和计算")
    print("- ✅ 便于维护和调试")

if __name__ == "__main__":
    print("🚀 标准化语法测试")
    print("=" * 60)
    
    # 测试工具名称映射
    test_tool_name_mapping()

    # 测试基础语法
    test_standardized_syntax()
    
    # 测试复杂示例
    test_complex_example()
    
    # 测试您的原始需求
    test_your_original_example()
    
    # 显示语法指南
    show_syntax_guide()
    
    print("\n" + "=" * 60)
    print("🎉 标准化语法测试完成！")
    print("\n💡 建议:")
    print("1. 使用标准化语法编写复杂提示词")
    print("2. 明确指定每个操作的类型和参数")
    print("3. 利用变量系统传递数据")
    print("4. 通过计算命令处理复杂逻辑")
