"""
费用数据模型
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import date, datetime
import json

@dataclass
class ExpenseRecord:
    """费用记录模型"""
    billaccountpaymentdetail_id: str
    payment_code: str
    preg_id: str
    preg_name: str
    reg_id: str
    reg_name: str
    billamount_date: date
    paymentdetail_note: Optional[str]
    mandatory_note: Optional[str]
    search_keywords: Optional[str]
    auditing_state: Optional[int]
    last_review_result: Optional[str]
    last_review_comment: Optional[str]
    rpt_month: Optional[str]  # 年月筛选字段，格式：YYYYMM
    # 有默认值的字段必须放在最后
    billamount_startdate: Optional[date] = None  # 账单金额开始日期
    billamount_enddate: Optional[date] = None    # 账单金额结束日期

    # AI分类结果字段
    ai_category: Optional[str] = None
    ai_confidence: Optional[float] = None
    ai_classified_time: Optional[datetime] = None

@dataclass
class RemarkDetail:
    """备注详情模型"""
    count: int
    dictgroup_name: Optional[str]
    dict_name: Optional[str]
    content: str
    
    @classmethod
    def from_json(cls, json_str: str) -> List['RemarkDetail']:
        """从JSON字符串解析备注详情"""
        if not json_str:
            return []
        
        try:
            data = json.loads(json_str)
            if not isinstance(data, list):
                return []
            
            remarks = []
            for item in data:
                if isinstance(item, dict) and 'count' in item and 'content' in item:
                    remarks.append(cls(
                        count=item.get('count', 0),
                        dictgroup_name=item.get('dictgroup_name'),
                        dict_name=item.get('dict_name'),
                        content=item.get('content', '')
                    ))
            
            # 按count排序
            remarks.sort(key=lambda x: x.count)
            return remarks
        except (json.JSONDecodeError, Exception):
            return []

@dataclass
class ClassificationResult:
    """分类结果模型"""
    category: str
    confidence: float
    reason: Optional[str] = None

@dataclass
class SamplingConfig:
    """采样配置模型"""
    sample_size: int = 800
    confidence_level: float = 0.95
    margin_error: float = 0.05
    stratify_by_city: bool = True
    stratify_by_month: bool = True

@dataclass
class ExportRequest:
    """导出请求模型"""
    expense_type: str  # 费用类型（表名）
    rpt_month: str     # 年月，格式：YYYYMM
    city_id: str       # 地市ID
    category: Optional[str] = None  # 分类（用于明细导出）
