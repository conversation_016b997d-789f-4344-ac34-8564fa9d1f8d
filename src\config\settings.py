"""
配置管理模块 - 支持多环境配置
"""

import os
from typing import Optional
from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings

def load_environment_config():
    """
    根据APP_ENV环境变量加载对应的配置文件
    支持的环境：dev, test, staging, prod
    """
    # 环境名称映射
    env_mapping = {
        'dev': 'dev',
        'development': 'dev',
        'test': 'test',
        'testing': 'test',
        'staging': 'staging',
        'prod': 'prod',
        'production': 'prod'
    }

    # 获取当前环境，默认为dev
    current_env = os.getenv('APP_ENV', 'dev').lower()
    env_name = env_mapping.get(current_env, 'dev')

    # 配置文件加载顺序（后加载的会覆盖先加载的）
    config_files = [
        '.env',  # 基础配置
        f'config/.env.{env_name}',  # 环境特定配置
        'config/.env.local'  # 本地覆盖配置
    ]

    # 按顺序加载配置文件
    for config_file in config_files:
        if os.path.exists(config_file):
            load_dotenv(config_file, override=True)
            print(f"已加载配置文件: {config_file}")
        else:
            print(f"配置文件不存在，跳过: {config_file}")

    print(f"当前环境: {env_name} (APP_ENV={current_env})")
    return env_name

# 加载环境配置
current_environment = load_environment_config()


class Settings(BaseSettings):
    """应用配置类 - 支持多环境配置"""

    # 环境信息
    environment: str = Field(default=current_environment, description="当前运行环境")

    # 应用基础配置
    app_name: str = Field(default="AI Chat LangChain", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # 本地LLM配置
    local_llm_enabled: bool = Field(default=False, env="LOCAL_LLM_ENABLED")
    local_llm_api_key: Optional[str] = Field(default=None, env="LOCAL_LLM_API_KEY")
    local_llm_model_url: Optional[str] = Field(default=None, env="LOCAL_LLM_MODEL_URL")
    local_llm_model_name: Optional[str] = Field(default=None, env="LOCAL_LLM_MODEL_NAME")
    local_llm_temperature: float = Field(default=0.7, env="LOCAL_LLM_TEMPERATURE")
    local_llm_timeout: int = Field(default=60, env="LOCAL_LLM_TIMEOUT")

    # Agent配置
    agent_max_iterations: int = Field(default=10, env="AGENT_MAX_ITERATIONS")
    agent_timeout: int = Field(default=120, env="AGENT_TIMEOUT")

    # 服务器配置
    server_host: str = Field(default="0.0.0.0", env="SERVER_HOST")
    server_port: int = Field(default=8001, env="SERVER_PORT")

    # 本地BGE Embedding配置
    local_bge_enabled: bool = Field(default=False, env="LOCAL_BGE_ENABLED")
    local_bge_api_url: Optional[str] = Field(default=None, env="LOCAL_BGE_API_URL")
    local_bge_api_key: Optional[str] = Field(default=None, env="LOCAL_BGE_API_KEY")
    local_bge_model_name: str = Field(default="bge-large-zh-v1.5", env="LOCAL_BGE_MODEL_NAME")
    local_bge_timeout: int = Field(default=30, env="LOCAL_BGE_TIMEOUT")
    
    # 本地BGE备用服务配置
    local_bge_fallback_url: Optional[str] = Field(default=None, env="LOCAL_BGE_FALLBACK_URL")
    local_bge_fallback_key: Optional[str] = Field(default=None, env="LOCAL_BGE_FALLBACK_KEY")
    local_bge_fallback_timeout: int = Field(default=15, env="LOCAL_BGE_FALLBACK_TIMEOUT")
    
    # OpenAI配置
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_api_base: str = Field(default="https://api.openai.com/v1", env="OPENAI_API_BASE")
    openai_model: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    openai_temperature: float = Field(default=0.7, env="OPENAI_TEMPERATURE")
    openai_max_tokens: int = Field(default=1000, env="OPENAI_MAX_TOKENS")
    
    # Azure OpenAI配置
    azure_openai_enabled: bool = Field(default=False, env="AZURE_OPENAI_ENABLED")
    azure_openai_api_key: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_api_version: str = Field(default="2023-12-01-preview", env="AZURE_OPENAI_API_VERSION")
    azure_openai_deployment_name: Optional[str] = Field(default=None, env="AZURE_OPENAI_DEPLOYMENT_NAME")
    
    # Anthropic配置
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # MySQL数据库配置
    mysql_host: str = Field(default="localhost", env="MYSQL_HOST")
    mysql_port: int = Field(default=3306, env="MYSQL_PORT")
    mysql_user: str = Field(default="root", env="MYSQL_USER")
    mysql_password: str = Field(default="password", env="MYSQL_PASSWORD")
    mysql_database: str = Field(default="test", env="MYSQL_DATABASE")
    mysql_charset: str = Field(default="utf8mb4", env="MYSQL_CHARSET")
    
    # PostgreSQL数据库配置
    postgresql_host: str = Field(default="localhost", env="POSTGRESQL_HOST")
    postgresql_port: int = Field(default=5432, env="POSTGRESQL_PORT")
    postgresql_user: str = Field(default="postgres", env="POSTGRESQL_USER")
    postgresql_password: str = Field(default="password", env="POSTGRESQL_PASSWORD")
    postgresql_database: str = Field(default="test", env="POSTGRESQL_DATABASE")
    
    # LangSmith配置
    langchain_tracing_v2: bool = Field(default=False, env="LANGCHAIN_TRACING_V2")
    langchain_endpoint: str = Field(default="https://api.smith.langchain.com", env="LANGCHAIN_ENDPOINT")
    langchain_api_key: Optional[str] = Field(default=None, env="LANGCHAIN_API_KEY")
    langchain_project: str = Field(default="ai-chat-langchain", env="LANGCHAIN_PROJECT")
    
    # 向量数据库配置
    pinecone_api_key: Optional[str] = Field(default=None, env="PINECONE_API_KEY")
    pinecone_environment: Optional[str] = Field(default=None, env="PINECONE_ENVIRONMENT")
    
    weaviate_url: str = Field(default="http://localhost:8080", env="WEAVIATE_URL")
    weaviate_api_key: Optional[str] = Field(default=None, env="WEAVIATE_API_KEY")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # MongoDB配置
    mongodb_url: str = Field(default="mongodb://localhost:27017", env="MONGODB_URL")
    mongodb_db_name: str = Field(default="langchain_db", env="MONGODB_DB_NAME")
    
    # PostgreSQL配置（兼容旧版）
    postgres_url: str = Field(default="postgresql://user:password@localhost:5432/langchain_db", env="POSTGRES_URL")
    
    # 知识库查询服务配置
    knowledge_search_api_url: str = Field(
        default="http://***********:8081/v1/workflows/run", 
        env="KNOWLEDGE_SEARCH_API_URL"
    )
    knowledge_search_api_key: str = Field(
        default="app-yBG3HXjw9NiGcxXzJ4QmHEFD", 
        env="KNOWLEDGE_SEARCH_API_KEY"
    )
    
    # SQL生成服务配置
    sql_generator_api_url: str = Field(
        default="http://***********:5000/api/v0/generate_sql",
        env="SQL_GENERATOR_API_URL"
    )
    sql_generator_api_key: str = Field(
        default="vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1",
        env="SQL_GENERATOR_API_KEY"
    )

    # 省份数据库映射配置
    province_database_mapping: str = Field(
        default="GZ:analysis_gz:贵州",
        env="PROVINCE_DATABASE_MAPPING"
    )

    # 远程embedding模型配置
    remote_embedding_enabled: bool = Field(
        default=False,
        env="REMOTE_EMBEDDING_ENABLED"
    )
    remote_embedding_url: str = Field(
        default="http://localhost:8001/embeddings",
        env="REMOTE_EMBEDDING_URL"
    )
    remote_embedding_api_key: str = Field(
        default="",
        env="REMOTE_EMBEDDING_API_KEY"
    )
    remote_embedding_model: str = Field(
        default="text-embedding-ada-002",
        env="REMOTE_EMBEDDING_MODEL"
    )
    
    def get_province_database_mapping(self) -> dict:
        """
        解析省份数据库映射配置

        Returns:
            dict: 省份代码到数据库配置的映射
            格式: {
                'GZ': {'analysis': 'analysis_gz', 'prvName': '贵州'},
                'NX': {'analysis': 'analysis_nx', 'prvName': '宁夏'},
                ...
            }
        """
        mapping = {}
        if self.province_database_mapping:
            # 解析格式：省份代码:数据库名称:省份名称,省份代码:数据库名称:省份名称,...
            for item in self.province_database_mapping.split(','):
                if ':' in item:
                    parts = item.strip().split(':')
                    if len(parts) >= 3:
                        province_code = parts[0].strip()
                        database_name = parts[1].strip()
                        province_name = parts[2].strip()
                        mapping[province_code] = {
                            'analysis': database_name,
                            'prvName': province_name
                        }
        return mapping

    def get_database_name_by_province(self, province_code: str) -> Optional[str]:
        """
        根据省份代码获取数据库名称

        Args:
            province_code: 省份代码，如 'GZ'

        Returns:
            str: 数据库名称，如 'analysis_gz'，如果未找到返回None
        """
        mapping = self.get_province_database_mapping()
        province_config = mapping.get(province_code.upper())
        return province_config['analysis'] if province_config else None

    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局配置实例
settings = Settings()