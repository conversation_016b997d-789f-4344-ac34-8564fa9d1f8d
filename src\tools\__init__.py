"""
工具模块
包含各种AI Agent可以使用的工具
"""

from .integrated_sql_tools import IntegratedSQLTool
from .knowledge_search_tools import KnowledgeSearchTool


def get_all_tools():
    """
    获取所有可用工具
    
    Returns:
        list: 工具实例列表
    """
    tools = []
    
    # 添加集成SQL工具
    sql_tool = IntegratedSQLTool()
    tools.append(sql_tool)
    
    # 添加知识库查询工具
    knowledge_tool = KnowledgeSearchTool()
    tools.append(knowledge_tool)
    
    return tools


# 导出主要工具类
__all__ = [
    'IntegratedSQLTool',
    'KnowledgeSearchTool',
    'get_all_tools'
] 