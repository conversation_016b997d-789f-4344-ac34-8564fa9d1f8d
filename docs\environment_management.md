# 多环境配置管理指南

## 概述

本项目支持多环境配置管理，可以轻松在开发、测试、预发布和生产环境之间切换，无需手动修改代码。

## 环境类型

| 环境名称 | 简写 | 配置文件 | 用途 |
|---------|------|----------|------|
| 开发环境 | dev | config/.env.dev | 本地开发调试 |
| 测试环境 | test | config/.env.test | 自动化测试 |
| 预发布环境 | staging | config/.env.staging | 生产前验证 |
| 生产环境 | prod | config/.env.prod | 正式生产环境 |

## 使用方法

### 1. 启动不同环境

```bash
# Linux/Mac 语法
# 开发环境 (默认)
python start_analysis_agent_server.py

# 测试环境
APP_ENV=test python start_analysis_agent_server.py

# 生产环境
APP_ENV=prod python start_analysis_agent_server.py
```

```powershell
# Windows PowerShell 语法
# 开发环境 (默认)
python start_analysis_agent_server.py

# 测试环境
$env:APP_ENV="test"; python start_analysis_agent_server.py

# 生产环境
$env:APP_ENV="prod"; python start_analysis_agent_server.py
```

**注意**：当前主应用暂不支持多环境，上述命令仅为示例。如需多环境支持，请联系开发者。

### 2. 支持的环境变量值

支持简写和全名：
- `dev`, `development` → 开发环境
- `test`, `testing` → 测试环境
- `staging` → 预发布环境
- `prod`, `production` → 生产环境

### 3. 配置文件加载顺序

配置文件按以下顺序加载（后加载的会覆盖先加载的）：

1. `.env` - 基础配置
2. `config/.env.{环境名}` - 环境特定配置
3. `config/.env.local` - 本地覆盖配置

### 4. 本地开发配置覆盖

如果需要覆盖某些配置（如数据库连接、API密钥等），可以创建 `config/.env.local` 文件：

```bash
# 复制示例文件
cp config/.env.local.example config/.env.local

# 编辑本地配置
vim config/.env.local
```

**注意：** `.env.local` 文件不会被git跟踪，可以安全地存放个人配置。

## 环境配置差异

### 开发环境 (dev)
- DEBUG=true
- LOG_LEVEL=DEBUG
- 使用本地数据库
- 启用详细日志
- 服务器绑定到 127.0.0.1

### 测试环境 (test)
- DEBUG=false
- LOG_LEVEL=INFO
- 使用测试数据库
- 启用LangSmith追踪
- 较短的超时时间

### 预发布环境 (staging)
- DEBUG=false
- LOG_LEVEL=INFO
- 使用预发布数据库
- 接近生产环境的配置
- 用于最终验证

### 生产环境 (prod)
- DEBUG=false
- LOG_LEVEL=WARNING
- 使用生产数据库集群
- 最高安全性配置
- 性能优化设置

## 配置管理最佳实践

### 1. 敏感信息管理
- 生产环境的敏感配置（密码、API密钥）不要提交到git
- 使用环境变量或密钥管理系统
- 在部署时通过CI/CD系统注入敏感配置

### 2. 配置文件维护
- 定期同步各环境的配置结构
- 新增配置项时，同时更新所有环境文件
- 使用 `env.example` 作为配置模板

### 3. 部署建议
```bash
# 生产部署示例
export APP_ENV=prod
export MYSQL_PASSWORD=actual_prod_password
export LOCAL_LLM_API_KEY=actual_prod_api_key
python start_analysis_agent_server.py
```

## 代码中使用环境信息

```python
from src.config.settings import settings

# 检查当前环境
if settings.is_development():
    print("开发环境特定逻辑")
elif settings.is_production():
    print("生产环境特定逻辑")

# 获取环境信息
env_info = settings.get_environment_info()
print(f"当前环境: {env_info['environment']}")
```

## 故障排除

### 1. 配置文件未找到
确保配置文件存在且路径正确：
```bash
ls -la config/.env.*
```

### 2. 环境变量未生效
检查环境变量设置：
```bash
echo $APP_ENV
```

### 3. 配置加载顺序问题
查看启动日志，确认配置文件加载顺序：
```
已加载配置文件: .env
已加载配置文件: config/.env.dev
当前环境: dev (APP_ENV=dev)
```

## 迁移指南

从单一 `.env` 文件迁移到多环境配置：

1. 备份现有 `.env` 文件
2. 根据当前配置创建对应的环境配置文件
3. 测试各环境配置是否正确加载
4. 更新部署脚本使用新的环境变量
