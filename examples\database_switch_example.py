#!/usr/bin/env python3
"""
数据库切换功能示例
演示如何在复杂提示词中使用数据库切换功能
"""

import sys
import os
import uuid
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager, ComplexPrompt

def create_database_switch_example():
    """创建数据库切换示例提示词"""
    print("🔄 创建数据库切换功能示例")
    print("=" * 60)
    
    try:
        manager = get_prompt_vectorstore_manager()
        
        # 示例1：省份对比分析（需要查询公共库）
        prompt1 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="省份电费对比分析",
            description="对比本省电费与全国平均水平，提供详细的对比分析报告",
            trigger_questions=[
                "本省电费与全国对比如何",
                "我们省电费水平怎么样",
                "电费全国对比分析",
                "省份电费对比情况",
                "与全国平均电费对比"
            ],
            processing_steps=[
                "查询本省最近3个月的电费数据，包括总额、单价、构成",
                "[切换数据库:JT] 查询全国各省电费平均水平和排名数据",
                "[切换数据库:原始] 基于全国数据对比分析本省电费水平",
                "计算本省电费与全国平均的差异百分比",
                "分析本省电费的优势和劣势",
                "提供电费优化建议和改进方向"
            ],
            response_format="""
# 📊 省份电费对比分析报告

## 📈 本省电费概况
- 查询时间范围：[具体时间]
- 本省总电费：[金额] 万元
- 本省平均单价：[单价] 元/kWh

## 🌍 全国对比数据
- 全国平均电费：[金额] 万元
- 全国平均单价：[单价] 元/kWh
- 本省全国排名：第 [排名] 位

## 📊 对比分析
- 电费总额差异：[高于/低于] 全国平均 [百分比]%
- 单价差异：[高于/低于] 全国平均 [百分比]%
- 主要差异原因：[具体分析]

## 💡 优化建议
1. **[优化方向1]**：[具体建议]
2. **[优化方向2]**：[具体建议]
3. **[优化方向3]**：[具体建议]

## 🎯 改进目标
[基于对比分析提出的具体改进目标]
            """,
            priority=4,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例2：多省份数据汇总分析
        prompt2 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="多省份数据汇总分析",
            description="汇总分析多个省份的关键指标数据，提供综合对比报告",
            trigger_questions=[
                "多省份数据汇总",
                "各省数据对比分析",
                "省际数据汇总报告",
                "跨省数据分析",
                "区域数据汇总"
            ],
            processing_steps=[
                "查询本省的关键业务指标数据",
                "[切换数据库:JT] 查询全国汇总数据和各省排名",
                "[切换数据库:GZ] 查询贵州省对比数据",
                "[切换数据库:BJ] 查询北京市对比数据",
                "[切换数据库:原始] 汇总所有数据进行综合分析",
                "生成多维度对比分析报告"
            ],
            response_format="""
# 📊 多省份数据汇总分析报告

## 📋 数据汇总概览
| 省份 | 指标1 | 指标2 | 指标3 | 排名 |
|------|-------|-------|-------|------|
| 本省 | [数据] | [数据] | [数据] | [排名] |
| 贵州 | [数据] | [数据] | [数据] | [排名] |
| 北京 | [数据] | [数据] | [数据] | [排名] |
| 全国平均 | [数据] | [数据] | [数据] | - |

## 📈 趋势分析
- **本省表现**：[具体分析]
- **对比优势**：[优势分析]
- **改进空间**：[改进建议]

## 🎯 综合评价
[基于多省份对比的综合评价和建议]
            """,
            priority=3,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 添加示例提示词
        prompts = [prompt1, prompt2]
        success_count = 0
        
        for prompt in prompts:
            if manager.add_complex_prompt(prompt):
                success_count += 1
                print(f"✅ 成功添加示例提示词: {prompt.title}")
            else:
                print(f"❌ 添加示例提示词失败: {prompt.title}")
        
        print(f"\n📊 总结: 成功添加 {success_count}/{len(prompts)} 个示例提示词")
        
        if success_count > 0:
            print("\n🎯 使用说明:")
            print("1. 启动API服务器: python examples/analysis_agent_server.py")
            print("2. 测试问题示例:")
            print("   - '本省电费与全国对比如何'")
            print("   - '多省份数据汇总分析'")
            print("3. 系统会自动:")
            print("   - 识别数据库切换命令")
            print("   - 在不同数据库间切换查询")
            print("   - 汇总多数据源的结果")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 创建示例失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_switch_parsing():
    """测试数据库切换命令解析"""
    print("\n🧪 测试数据库切换命令解析")
    print("=" * 60)
    
    from src.agents.vector_enhanced_agent import VectorEnhancedAgent
    
    # 创建测试实例
    agent = VectorEnhancedAgent()
    agent.original_province_code = "GZ"  # 模拟原始省份
    
    # 测试处理步骤
    test_steps = [
        "查询本省电费数据",
        "[切换数据库:JT] 查询全国平均数据",
        "分析数据差异",
        "[切换数据库:BJ] 查询北京数据进行对比",
        "[切换数据库:原始] 回到原始数据库",
        "生成最终报告"
    ]
    
    print("📝 原始处理步骤:")
    for i, step in enumerate(test_steps, 1):
        print(f"  {i}. {step}")
    
    # 解析数据库切换命令
    enhanced_steps = agent._parse_database_switch_commands(test_steps)
    
    print("\n🔄 解析后的处理步骤:")
    for i, step in enumerate(enhanced_steps, 1):
        print(f"  {i}. {step}")
    
    print("\n✅ 数据库切换命令解析测试完成")

if __name__ == "__main__":
    print("🚀 数据库切换功能示例")
    print("=" * 60)
    
    # 创建示例提示词
    if create_database_switch_example():
        # 测试解析功能
        test_database_switch_parsing()
        
        print("\n" + "=" * 60)
        print("🎉 数据库切换功能示例创建完成！")
        print("\n💡 功能特点:")
        print("- ✅ 支持在处理步骤中切换数据库")
        print("- ✅ 支持切换到公共库(JT)查询全国数据")
        print("- ✅ 支持切换到其他省份数据库")
        print("- ✅ 支持切换回原始数据库")
        print("- ✅ 自动解析和执行数据库切换命令")
    else:
        print("\n❌ 示例创建失败，请检查配置")
