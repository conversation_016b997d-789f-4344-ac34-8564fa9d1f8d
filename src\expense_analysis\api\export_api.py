"""
费用分类导出API
"""
import os
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.responses import FileResponse
import tempfile
import logging

from ..services.data_service import DataService
from ..auth import verify_token, verify_token_with_database
from ..auth_config import AuthConfig
from fastapi import Request

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title="费用分析导出API",
    description="提供费用分类统计和明细数据的导出功能",
    root_path="/ele-agent"  # 让所有生成的URL都加上/ele-agent前缀
)

# 添加请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有请求的详细信息"""
    print(f"🔍 [DEBUG] 中间件被触发: {request.method} {request.url}")

    start_time = datetime.now()

    # 使用print确保输出显示
    print("=" * 80)
    print(f"📥 [REQUEST] {request.method} {request.url}")
    print(f"🌐 [CLIENT] {request.client.host}:{request.client.port}")

    # 记录Headers
    print("📋 [HEADERS]:")
    for name, value in request.headers.items():
        # 对敏感信息进行脱敏
        if name.lower() == "authorization":
            if value.startswith("Bearer "):
                token = value[7:]  # 去掉 "Bearer "
                masked_token = token[:10] + "..." + token[-6:] if len(token) > 16 else "***"
                print(f"  {name}: Bearer {masked_token}")
            else:
                print(f"  {name}: {value[:20]}...")
        else:
            print(f"  {name}: {value}")

    # 记录查询参数
    if request.query_params:
        print("🔍 [QUERY PARAMS]:")
        for name, value in request.query_params.items():
            print(f"  {name}: {value}")

    # 处理请求
    response = await call_next(request)

    # 记录响应信息
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    print(f"📤 [RESPONSE] {response.status_code} - {duration:.3f}s")

    # 如果是401错误，记录更多调试信息
    if response.status_code == 401:
        print("🚫 [AUTH FAILED] 鉴权失败详情:")
        auth_header = request.headers.get("authorization", "")
        if not auth_header:
            print("  ❌ 未提供Authorization头")
        elif not auth_header.startswith("Bearer "):
            print(f"  ❌ Authorization格式错误: {auth_header[:20]}...")
        else:
            token = auth_header[7:]
            print(f"  🔑 Token: {token[:10]}...{token[-6:] if len(token) > 16 else '***'}")

            # 检查token是否在有效列表中
            try:
                valid_tokens = AuthConfig.get_valid_tokens()
                if token in valid_tokens:
                    print("  ✅ Token在有效列表中，可能是其他鉴权问题")
                    database = AuthConfig.get_database_by_token(token)
                    print(f"  🗄️ 对应数据库: {database}")
                else:
                    print("  ❌ Token不在有效列表中")
                    print(f"  📊 当前有效Token数量: {len(valid_tokens)}")
                    # 显示前几个有效token的前缀
                    sample_tokens = [t[:16] + "..." for t in list(valid_tokens)[:3]]
                    print(f"  📋 有效Token示例: {sample_tokens}")
            except Exception as e:
                print(f"  ⚠️ 检查Token时出错: {e}")

    print("=" * 80)

    return response

# 注意：data_service 将在每个接口中动态创建，以支持数据库切换

@app.get("/export/statistics")
async def export_statistics(
    rpt_month: Optional[str] = Query(None, description="报账月份(YYYYMM)"),
    preg_id: Optional[str] = Query(None, description="地市ID"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """导出分类统计结果"""
    try:
        # 从鉴权信息中获取token和数据库
        token, database = auth_info

        # 创建指定数据库的数据服务实例
        data_service = DataService(database_name=database)

        filters = {}
        if rpt_month:
            filters['rpt_month'] = rpt_month
        if preg_id:
            filters['preg_id'] = preg_id

        # 获取统计数据
        stats = data_service.get_classification_statistics(filters)
        
        if not stats:
            raise HTTPException(status_code=404, detail="未找到分类数据")
        
        # 转换为DataFrame
        df = pd.DataFrame([
            {'分类类别': category, '数量': count, '占比': f"{count/sum(stats.values())*100:.1f}%"}
            for category, count in stats.items()
        ])
        
        # 生成临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False, encoding='utf-8') as f:
            temp_path = f.name
        
        # 写入Excel
        with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='分类统计', index=False)
        
        # 生成文件名（包含数据库信息）
        db_suffix = database.replace('analysis_', '') if database.startswith('analysis_') else database
        filename = f"费用分类统计_{db_suffix}_{rpt_month or '全部'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return FileResponse(
            path=temp_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/export/details")
async def export_details(
    category: str = Query(..., description="分类类别"),
    rpt_month: Optional[str] = Query(None, description="报账月份(YYYYMM)"),
    preg_id: Optional[str] = Query(None, description="地市ID"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """导出指定分类的明细数据"""
    try:
        # 从鉴权信息中获取token和数据库
        token, database = auth_info

        # 创建指定数据库的数据服务实例
        data_service = DataService(database_name=database)

        filters = {}
        if rpt_month:
            filters['rpt_month'] = rpt_month
        if preg_id:
            filters['preg_id'] = preg_id

        # 获取明细数据
        details = data_service.get_classification_details(category, filters)
        
        if not details:
            raise HTTPException(status_code=404, detail=f"未找到分类 '{category}' 的明细数据")
        
        # 转换为DataFrame
        df = pd.DataFrame(details)
        
        # 重命名列
        column_mapping = {
            'billaccountpaymentdetail_id': '缴费明细ID',
            'payment_code': '缴费单编码',
            'preg_name': '地市名称',
            'reg_name': '区县名称',
            'rpt_month': '报账月份',
            'original_remark': '原始备注',
            'ai_category': 'AI分类',
            'ai_confidence': '置信度'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 格式化置信度
        if '置信度' in df.columns:
            df['置信度'] = df['置信度'].apply(lambda x: f"{x:.2%}" if pd.notnull(x) else "")
        
        # 生成临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False, encoding='utf-8') as f:
            temp_path = f.name
        
        # 写入Excel
        with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=f'{category}明细', index=False)
        
        # 生成文件名（包含数据库信息）
        db_suffix = database.replace('analysis_', '') if database.startswith('analysis_') else database
        filename = f"费用明细_{category}_{db_suffix}_{rpt_month or '全部'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return FileResponse(
            path=temp_path,
            filename=filename,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/api/statistics")
async def get_statistics(
    request: Request,
    rpt_month: Optional[str] = Query(None, description="报账月份(YYYYMM)"),
    preg_id: Optional[str] = Query(None, description="地市ID"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """获取分类统计数据（JSON格式）"""

    # 记录请求详情
    print("=" * 80)
    print(f"📥 [REQUEST] {request.method} {request.url}")
    print(f"🌐 [CLIENT] {request.client.host}:{request.client.port}")
    print("📋 [HEADERS]:")
    for name, value in request.headers.items():
        if name.lower() == "authorization":
            if value.startswith("Bearer "):
                token_value = value[7:]
                masked_token = token_value[:10] + "..." + token_value[-6:] if len(token_value) > 16 else "***"
                print(f"  {name}: Bearer {masked_token}")
            else:
                print(f"  {name}: {value[:20]}...")
        else:
            print(f"  {name}: {value}")

    if request.query_params:
        print("🔍 [QUERY PARAMS]:")
        for name, value in request.query_params.items():
            print(f"  {name}: {value}")

    try:
        # 从鉴权信息中获取token和数据库
        token, database = auth_info
        print(f"✅ [AUTH] Token验证成功，数据库: {database}")

        # 创建指定数据库的数据服务实例
        data_service = DataService(database_name=database)

        filters = {}
        if rpt_month:
            filters['rpt_month'] = rpt_month
        if preg_id:
            filters['preg_id'] = preg_id

        stats = data_service.get_classification_statistics(filters)
        total = sum(stats.values())
        
        result = {
            'total': total,
            'database': database,  # 添加数据库信息
            'categories': [
                {
                    'category': category,
                    'count': count,
                    'percentage': round(count / total * 100, 1) if total > 0 else 0
                }
                for category, count in stats.items()
            ],
            'filters': filters
        }
        
        print(f"📤 [RESPONSE] 200 - 成功返回 {len(result['categories'])} 个分类")
        print("=" * 80)
        return result

    except Exception as e:
        print(f"❌ [ERROR] 获取统计数据失败: {str(e)}")
        print("=" * 80)
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")


@app.get("/health")
async def health_check():
    """健康检查接口（无需鉴权）"""
    return {
        "status": "healthy",
        "service": "费用分析API",
        "timestamp": datetime.now().isoformat(),
        "auth_enabled": AuthConfig.ENABLE_AUTH
    }


@app.get("/auth/info")
async def auth_info(token: str = Depends(verify_token)):
    """获取当前token信息（需要鉴权）"""
    return {
        "message": "鉴权成功",
        "token_valid": True,
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
