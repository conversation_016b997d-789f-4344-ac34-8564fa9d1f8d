"""
简单聊天应用示例
演示如何使用ChatChain进行基础对话
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory
from src.chains import ChatChain
from src.config import settings


def main():
    """主函数"""
    print("=== AI Chat LangChain 简单聊天示例 ===\n")
    
    try:
        # 模型选择
        print("请选择要使用的模型:")
        print("1. OpenAI (需要API密钥)")
        print("2. Anthropic Claude (需要API密钥)")
        print("3. 本地模型 (需要配置本地服务)")
        
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            provider = "openai"
            model_name = input("请输入模型名称 (默认: gpt-3.5-turbo): ").strip() or "gpt-3.5-turbo"
        elif choice == "2":
            provider = "anthropic"
            model_name = input("请输入模型名称 (默认: claude-3-haiku-20240307): ").strip() or "claude-3-haiku-20240307"
        elif choice == "3":
            provider = "local"
            model_name = None  # 使用配置文件中的模型名称
            print("使用本地模型，请确保已在.env文件中配置相关参数")
        else:
            print("无效选择，使用默认OpenAI模型")
            provider = "openai"
            model_name = "gpt-3.5-turbo"
        
        # 创建语言模型
        print(f"\n正在初始化{provider}模型...")
        llm = LLMFactory.create_chat_model(
            provider=provider,
            model_name=model_name,
            temperature=0.7
        )
        print("✅ 模型初始化成功!")
        
        # 创建聊天链
        chat_chain = ChatChain(
            llm=llm,
            memory_type="buffer",  # 使用缓冲记忆
            session_id="simple_chat_session",
            storage_type="memory"
        )
        
        print("\n✅ 聊天系统已启动!")
        print("💡 输入 'quit'、'exit' 或 'q' 退出程序")
        print("💡 输入 'clear' 清空聊天记忆")
        print("💡 输入 'info' 查看记忆信息")
        print("-" * 50)
        
        # 开始聊天循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 您: ").strip()
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                elif user_input.lower() == 'clear':
                    chat_chain.clear_memory()
                    print("🗑️ 聊天记忆已清空")
                    continue
                elif user_input.lower() == 'info':
                    memory_info = chat_chain.get_memory_info()
                    print(f"📊 记忆信息: {memory_info}")
                    continue
                elif not user_input:
                    print("⚠️ 请输入有效内容")
                    continue
                
                # 获取AI回复
                print("🤖 AI: ", end="", flush=True)
                response = chat_chain.chat(user_input)
                print(response)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("请检查网络连接和API配置")
                continue
                
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        print("\n💡 解决建议:")
        if "OpenAI" in str(e):
            print("- 检查 OPENAI_API_KEY 环境变量是否设置")
            print("- 检查网络连接是否正常")
        elif "Anthropic" in str(e):
            print("- 检查 ANTHROPIC_API_KEY 环境变量是否设置")
            print("- 检查网络连接是否正常")
        elif "本地" in str(e) or "local" in str(e).lower():
            print("- 检查本地模型服务是否运行")
            print("- 检查 LOCAL_LLM_ENABLED、LOCAL_LLM_API_KEY、LOCAL_LLM_MODEL_URL、LOCAL_LLM_MODEL_NAME 环境变量")
            print("- 检查网络连接和服务可用性")
        print("- 检查 .env 文件配置")


if __name__ == "__main__":
    main() 