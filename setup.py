"""
AI Chat LangChain 安装配置
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="ai-chat-langchain",
    version="1.0.0",
    author="AI Chat Team",
    author_email="<EMAIL>",
    description="基于LangChain的智能对话系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/ai-chat-langchain",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "isort>=5.12.0",
        ],
        "all": [
            "unstructured[all-docs]>=0.11.0",
            "detectron2>=0.6",
            "layoutparser[layoutmodels,tesseract,ocr]>=0.3.4",
        ]
    },
    entry_points={
        "console_scripts": [
            "ai-chat=examples.simple_chat:main",
            "ai-rag=examples.rag_chat:main",
        ],
    },
    keywords="langchain, chatbot, ai, nlp, rag, vector-database",
    project_urls={
        "Bug Reports": "https://github.com/your-username/ai-chat-langchain/issues",
        "Source": "https://github.com/your-username/ai-chat-langchain",
        "Documentation": "https://your-docs-url.com",
    },
) 