#!/usr/bin/env python3
"""
调试搜索功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager

def debug_search():
    """调试搜索功能"""
    print("🔍 调试搜索功能")
    print("=" * 60)
    
    try:
        manager = get_prompt_vectorstore_manager()
        
        # 1. 检查现有数据
        print("📋 检查现有提示词:")
        all_prompts = manager.get_all_prompts()
        print(f"总数量: {len(all_prompts)}")
        
        for i, prompt in enumerate(all_prompts, 1):
            print(f"\n{i}. {prompt.title}")
            print(f"   触发关键词: {prompt.trigger_keywords}")
            print(f"   描述: {prompt.description[:50]}...")
            
            # 显示向量化文本
            search_text = manager._build_search_text(prompt)
            print(f"   向量化文本: {search_text[:100]}...")
        
        # 2. 测试搜索
        print("\n" + "=" * 60)
        print("🧪 测试搜索功能:")
        
        test_queries = [
            "电费情况",
            "电费分析", 
            "当前电费情况如何",
            "铁塔服务费",
            "塔类服务费",
            "2025年4月铁塔服务费是多少",
            "指标对比",
            "异常数据"
        ]
        
        for query in test_queries:
            print(f"\n🔍 搜索: '{query}'")
            
            # 使用不同的阈值测试
            for threshold in [0.5, 0.6, 0.7, 0.8]:
                results = manager.search_complex_prompts(query, k=3, score_threshold=threshold)
                print(f"  阈值 {threshold}: {len(results)} 个结果")
                
                for prompt, similarity in results:
                    print(f"    - {prompt.title}: {similarity:.3f}")
            
            # 直接调用ChromaDB查看原始结果
            try:
                raw_results = manager.vectorstore.similarity_search_with_score(query, k=3)
                print(f"  原始结果: {len(raw_results)} 个")
                for doc, distance in raw_results:
                    similarity = 1.0 - distance
                    title = doc.metadata.get('title', 'Unknown')
                    print(f"    - {title}: 距离={distance:.3f}, 相似度={similarity:.3f}")
            except Exception as e:
                print(f"  原始搜索失败: {e}")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def test_embedding_directly():
    """直接测试embedding"""
    print("\n" + "=" * 60)
    print("🧪 直接测试Embedding:")
    
    try:
        from src.core.remote_embeddings import create_remote_embeddings
        
        embeddings = create_remote_embeddings()
        
        # 测试几个文本的向量化
        test_texts = [
            "电费情况",
            "电费分析", 
            "铁塔服务费",
            "当前电费情况如何"
        ]
        
        print("📊 向量化测试:")
        for text in test_texts:
            try:
                vector = embeddings.embed_query(text)
                print(f"  '{text}': 维度={len(vector)}, 前5个值={vector[:5]}")
            except Exception as e:
                print(f"  '{text}': 向量化失败 - {e}")
                
    except Exception as e:
        print(f"❌ Embedding测试失败: {e}")

def main():
    """主函数"""
    debug_search()
    test_embedding_directly()
    
    print("\n" + "=" * 60)
    print("💡 分析建议:")
    print("1. 检查向量化文本是否包含足够的关键信息")
    print("2. 检查相似度计算是否正确")
    print("3. 检查阈值设置是否合理")
    print("4. 检查embedding模型是否正常工作")

if __name__ == "__main__":
    main()
