<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型QA训练小助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #f1f8ff;
            font-weight: bold;
        }
        .priority-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .keyword-badge {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .step-item {
            margin-bottom: 8px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
        }
        .processing-steps-container {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            background-color: #ffffff;
        }
        .search-result {
            border-left: 4px solid #0d6efd;
        }
        .similarity-score {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 0.9rem;
        }
        #searchResults {
            max-height: 600px;
            overflow-y: auto;
        }
        .format-preview {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            border-top-color: #0d6efd;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .card-body {
            padding: 1rem;
        }
        .card-title {
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
        }
        .text-muted {
            font-weight: 500;
        }
        .badge {
            font-size: 0.75rem;
        }
        .question-badge {
            margin-right: 4px;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">大模型QA训练小助手</h1>
        
        <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="list-tab" data-bs-toggle="tab" data-bs-target="#list" type="button" role="tab" aria-controls="list" aria-selected="true">
                    <i class="bi bi-list-ul"></i> 场景训练列表
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="create-tab" data-bs-toggle="tab" data-bs-target="#create" type="button" role="tab" aria-controls="create" aria-selected="false">
                    <i class="bi bi-plus-circle"></i> 创建训练场景
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="search-tab" data-bs-toggle="tab" data-bs-target="#search" type="button" role="tab" aria-controls="search" aria-selected="false">
                    <i class="bi bi-search"></i> 搜索测试
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab" aria-controls="stats" aria-selected="false">
                    <i class="bi bi-bar-chart"></i> 统计信息
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="myTabContent">
            <!-- 提示词列表 -->
            <div class="tab-pane fade show active" id="list" role="tabpanel" aria-labelledby="list-tab">
                <div class="d-flex justify-content-between mb-3">
                    <h3>场景训练列表</h3>
                    <button class="btn btn-primary" onclick="refreshPromptList()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
                <div id="promptList" class="row">
                    <div class="text-center py-5">
                        <div class="loading"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
            
            <!-- 创建提示词 -->
            <div class="tab-pane fade" id="create" role="tabpanel" aria-labelledby="create-tab">
                <h3 class="mb-3">创建新训练场景</h3>
                <form id="createPromptForm">
                    <div class="mb-3">
                        <label for="title" class="form-label">标题</label>
                        <input type="text" class="form-control" id="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" rows="2" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="triggerQuestions" class="form-label">触发问题</label>
                        <textarea class="form-control" id="triggerQuestions" rows="3" placeholder="每行一个问题" required></textarea>
                        <div class="form-text">例如: 当前电费情况如何<br>电费情况怎么样<br>最近电费多少</div>
                    </div>
                    <div class="mb-3">
                        <label for="processingSteps" class="form-label">处理步骤</label>
                        <textarea class="form-control" id="processingSteps" rows="5" required></textarea>
                        <div class="form-text">每个段落一个步骤，段落之间用空行分隔</div>
                    </div>
                    <div class="mb-3">
                        <label for="responseFormat" class="form-label">回复格式要求</label>
                        <textarea class="form-control" id="responseFormat" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="priority" class="form-label">优先级</label>
                        <select class="form-select" id="priority">
                            <option value="1">1 - 低</option>
                            <option value="2">2 - 中</option>
                            <option value="3">3 - 高</option>
                            <option value="4">4 - 最高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="noThinkMode">
                            <label class="form-check-label" for="noThinkMode">
                                🚀 启用极速模式 (no_think)
                            </label>
                            <div class="form-text">启用后将关闭think标签，大幅提高响应速度，适用于复杂的多步骤分析任务</div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">创建</button>
                </form>
            </div>
            
            <!-- 搜索测试 -->
            <div class="tab-pane fade" id="search" role="tabpanel" aria-labelledby="search-tab">
                <h3 class="mb-3">搜索测试</h3>
                <div class="card mb-4">
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="mb-3">
                                <label for="searchQuery" class="form-label">搜索查询</label>
                                <input type="text" class="form-control" id="searchQuery" required>
                                <div class="form-text">输入用户可能的问题进行测试</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="resultCount" class="form-label">结果数量</label>
                                        <input type="number" class="form-control" id="resultCount" value="3" min="1" max="10">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="scoreThreshold" class="form-label">相似度阈值</label>
                                        <input type="number" class="form-control" id="scoreThreshold" value="0.3" min="0.1" max="1" step="0.05">
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">搜索</button>
                        </form>
                    </div>
                </div>
                
                <div id="searchResults">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="tab-pane fade" id="stats" role="tabpanel" aria-labelledby="stats-tab">
                <h3 class="mb-3">统计信息</h3>
                <div class="card">
                    <div class="card-body">
                        <div id="statsContent">
                            <div class="text-center py-5">
                                <div class="loading"></div>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 编辑提示词模态框 -->
    <div class="modal fade" id="editPromptModal" tabindex="-1" aria-labelledby="editPromptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPromptModalLabel">编辑提示词</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editPromptForm">
                        <input type="hidden" id="editPromptId">
                        <div class="mb-3">
                            <label for="editTitle" class="form-label">标题</label>
                            <input type="text" class="form-control" id="editTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="editDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="editDescription" rows="2" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTriggerQuestions" class="form-label">触发问题</label>
                            <textarea class="form-control" id="editTriggerQuestions" rows="3" placeholder="每行一个问题" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editProcessingSteps" class="form-label">处理步骤</label>
                            <textarea class="form-control" id="editProcessingSteps" rows="5" required></textarea>
                            <div class="form-text">每个段落一个步骤，段落之间用空行分隔</div>
                        </div>
                        <div class="mb-3">
                            <label for="editResponseFormat" class="form-label">回复格式要求</label>
                            <textarea class="form-control" id="editResponseFormat" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editPriority" class="form-label">优先级</label>
                            <select class="form-select" id="editPriority">
                                <option value="1">1 - 低</option>
                                <option value="2">2 - 中</option>
                                <option value="3">3 - 高</option>
                                <option value="4">4 - 最高</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editNoThinkMode">
                                <label class="form-check-label" for="editNoThinkMode">
                                    🚀 启用极速模式 (no_think)
                                </label>
                                <div class="form-text">启用后将关闭think标签，大幅提高响应速度</div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditButton">保存</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除提示词 "<span id="deletePromptTitle"></span>" 吗？</p>
                    <p class="text-danger">此操作无法撤销！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteButton">删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/prompt_management.js?v=2.4"></script>
</body>
</html>
