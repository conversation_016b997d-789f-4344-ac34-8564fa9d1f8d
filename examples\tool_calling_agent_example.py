"""
工具调用Agent使用示例
演示如何创建和使用具有工具调用能力的智能Agent
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory
from src.agents import ToolCallingAgent, AgentExecutor
from src.tools import get_all_tools
from src.config import settings


def display_tools():
    """显示可用工具"""
    print("=== 可用工具列表 ===")
    tools = get_all_tools()
    for i, tool in enumerate(tools, 1):
        print(f"{i}. {tool.name}: {tool.description}")
    print()


def test_agent_basic():
    """测试Agent基本功能"""
    print("=== Agent基本功能测试 ===")
    
    try:
        # 创建千问模型
        print("正在初始化千问模型...")
        llm = LLMFactory.create_chat_model(
            provider="local",
            temperature=0.7
        )
        print("✅ 千问模型初始化成功!")
        
        # 创建Agent
        agent = ToolCallingAgent(llm=llm)
        executor = AgentExecutor(agent=agent)
        
        print("✅ Agent初始化成功!")
        print("可用工具数量:", len(agent.tools))
        
        # 测试不同类型的查询
        test_queries = [
            "你好，请介绍一下自己",
            "帮我计算 (123 + 456) * 789 的结果",
            "搜索一下关于Python编程的信息",
            "请将文本'Hello, Agent!'写入文件test_output.txt"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 测试 {i}: {query} ---")
            result = executor.chat(query)
            
            print(f"回复: {result['response']}")
            if result['tool_used']:
                print(f"使用工具: {result['tool_call']['tool_name']}")
                print(f"工具结果: {result['tool_result']['success']}")
            print()
        
        # 显示统计信息
        stats = executor.get_stats()
        print("=== 执行统计 ===")
        print(f"总消息数: {stats['total_messages']}")
        print(f"工具调用次数: {stats['tool_calls']}")
        print(f"成功率: {stats['tool_success_rate']:.1%}")
        
        return executor
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None


def interactive_chat():
    """交互式聊天"""
    print("\n=== 交互式聊天模式 ===")
    
    try:
        # 创建千问模型和Agent
        llm = LLMFactory.create_chat_model(provider="local", temperature=0.7)
        agent = ToolCallingAgent(llm=llm)
        executor = AgentExecutor(agent=agent)
        
        print("🤖 智能Agent已启动!")
        print("💡 我可以使用以下工具:")
        for tool in agent.tools:
            print(f"   - {tool.name}: {tool.description}")
        
        print("\n💡 特殊命令:")
        print("   - 'quit' / 'exit' / 'q': 退出")
        print("   - 'clear': 清空对话历史")
        print("   - 'stats': 查看统计信息")
        print("   - 'tools': 查看可用工具")
        print("   - 'export': 导出对话记录")
        print("-" * 60)
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                elif user_input.lower() == 'clear':
                    executor.clear_history()
                    print("🗑️ 对话历史已清空")
                    continue
                elif user_input.lower() == 'stats':
                    stats = executor.get_stats()
                    tool_usage = executor.get_tool_usage_summary()
                    print("\n📊 统计信息:")
                    print(f"   总消息数: {stats['total_messages']}")
                    print(f"   工具调用次数: {stats['tool_calls']}")
                    print(f"   成功率: {stats['tool_success_rate']:.1%}")
                    if tool_usage:
                        print("   工具使用详情:")
                        for tool_name, usage in tool_usage.items():
                            print(f"     {tool_name}: {usage['successful_calls']}/{usage['total_calls']} 成功")
                    continue
                elif user_input.lower() == 'tools':
                    print("\n🔧 可用工具:")
                    for tool in agent.tools:
                        print(f"   - {tool.name}: {tool.description}")
                    continue
                elif user_input.lower() == 'export':
                    conversation = executor.export_conversation(format="text")
                    filename = f"conversation_export_{executor._get_timestamp().replace(':', '-').replace(' ', '_')}.txt"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(conversation)
                    print(f"📁 对话记录已导出到: {filename}")
                    continue
                elif not user_input:
                    print("⚠️ 请输入有效内容")
                    continue
                
                # 处理用户消息
                print("🤖 正在思考...", end="", flush=True)
                result = executor.chat(user_input)
                print("\r", end="")  # 清除"正在思考..."
                
                print(f"🤖 Agent: {result['response']}")
                
                # 显示工具使用信息
                if result['tool_used']:
                    tool_call = result['tool_call']
                    tool_result = result['tool_result']
                    print(f"   🔧 使用了工具: {tool_call['tool_name']}")
                    if tool_result['success']:
                        print(f"   ✅ 工具执行成功")
                    else:
                        print(f"   ❌ 工具执行失败: {tool_result.get('error', '未知错误')}")
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                continue
                
    except Exception as e:
        print(f"❌ 初始化失败: {e}")


def show_config():
    """显示当前配置"""
    print("=== 当前配置信息 ===")
    print(f"本地LLM启用: {settings.local_llm_enabled}")
    if settings.local_llm_enabled:
        print(f"  模型URL: {settings.local_llm_model_url}")
        print(f"  模型名称: {settings.local_llm_model_name}")
        print(f"  API密钥: {'已设置' if settings.local_llm_api_key else '未设置'}")
    else:
        print("⚠️ 本地LLM未启用，请在.env文件中配置")
    print()


def main():
    """主函数"""
    print("=== 工具调用Agent示例 ===\n")
    
    # 显示配置
    show_config()
    
    # 显示可用工具
    display_tools()
    
    # 检查配置
    if not settings.local_llm_enabled:
        print("❌ 请先在.env文件中启用和配置千问模型:")
        print("   LOCAL_LLM_ENABLED=true")
        print("   LOCAL_LLM_API_KEY=your_api_key")
        print("   LOCAL_LLM_MODEL_URL=your_model_url")
        print("   LOCAL_LLM_MODEL_NAME=your_model_name")
        return
    
    # 选择运行模式
    print("请选择运行模式:")
    print("1. 基本功能测试")
    print("2. 交互式聊天")
    print("3. 退出")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        executor = test_agent_basic()
        if executor:
            # 询问是否要继续交互式聊天
            continue_chat = input("\n是否要继续交互式聊天? (y/n): ").strip().lower()
            if continue_chat in ['y', 'yes']:
                # 使用现有的executor
                agent = executor.agent
                print("\n=== 切换到交互式聊天模式 ===")
                print("🤖 继续使用当前Agent进行交互...")
                
                while True:
                    try:
                        user_input = input("\n👤 您: ").strip()
                        
                        if user_input.lower() in ['quit', 'exit', 'q']:
                            print("👋 再见!")
                            break
                        elif not user_input:
                            continue
                        
                        result = executor.chat(user_input)
                        print(f"🤖 Agent: {result['response']}")
                        
                        if result['tool_used']:
                            print(f"   🔧 使用了工具: {result['tool_call']['tool_name']}")
                            
                    except KeyboardInterrupt:
                        print("\n\n👋 程序被用户中断，再见!")
                        break
                    except Exception as e:
                        print(f"\n❌ 发生错误: {e}")
                        continue
    
    elif choice == "2":
        interactive_chat()
    
    elif choice == "3":
        print("👋 再见!")
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main() 