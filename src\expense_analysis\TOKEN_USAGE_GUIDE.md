# 🔐 费用分析API Token使用指南

## 📋 概述

费用分析API为全国32个省份、总部和特殊区域提供了34个独立的Token，每个Token对应一个特定的数据库。用户通过不同的Token可以安全地访问对应省份的数据，无法跨省份访问数据。

**🔧 动态配置特性**: 数据库名称从环境变量`PROVINCE_DATABASE_MAPPING`动态读取，支持运行时修改数据库配置而无需重新部署代码。

## 🗺️ Token与省份数据库映射表

| 省份/区域 | 省份代码 | Token | 数据库名称 |
|----------|---------|-------|-----------|
| 宁夏 | NX | `nx_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1` | analysis_nx |
| 甘肃 | GS | `gs_9a5d6e3f8c2b7a4e1d9f6c3b8e5a2d7f4c1b9e6` | analysis_gs |
| 河北 | HE | `he_7c2f5b8e1a4d9c6f3b8e5a2d7f4c1b9e6a3d8f5` | analysis_he |
| 贵州 | GZ | `gz_6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4` | analysis_gz |
| 特殊区域 | XXL | `xxl_5a0d3c6f9b2e7a4d1c8f5b2e9a6d3c0f7b4e1a9` | xxltidb |
| 总部 | JT | `jt_4f9c2b5e8a1d7c0f3b6e9a2d5c8f1b4e7a0d3c6` | analysis_qg |
| 北京 | BJ | `bj_3e8b1c4f7a0d9c2f5b8e1a4d7c0f3b6e9a2d5c8` | analysis_bj |
| 天津 | TJ | `tj_2d7a0c3f6b9e2a5d8c1f4b7e0a3d6c9f2b5e8a1` | analysis_tj |
| 山西 | SX | `sx_1c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7c0` | analysis_sx |
| 内蒙古 | NM | `nm_0b5e8a1d4c7f0b3e6a9d2c5f8b1e4a7d0c3f6b9` | analysis_nm |
| 辽宁 | LN | `ln_f9a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6f9b2e5a8` | analysis_ln |
| 吉林 | JL | `jl_e8b3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7` | analysis_jl |
| 黑龙江 | HL | `hl_d7c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6` | analysis_hl |
| 上海 | SH | `sh_c6f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5` | analysis_sh |
| 江苏 | JS | `js_b5e0a3d6c9f2b5e8a1d4c7f0b3e6a9d2c5f8b1e4` | analysis_js |
| 浙江 | ZJ | `zj_a4d9c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3` | analysis_zj |
| 安徽 | AH | `ah_98c8f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2` | analysis_ah |
| 福建 | FJ | `fj_87b7e0a3d6c9f2b5e8a1d4c7f0b3e6a9d2c5f8b1` | analysis_fj |
| 江西 | JX | `jx_76a6d9c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0` | analysis_jx |
| 山东 | SD | `sd_65f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6f9` | analysis_sd |
| 河南 | HA | `ha_54e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8` | analysis_ha |
| 湖北 | HB | `hb_43d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7` | analysis_hb |
| 湖南 | HN | `hn_32c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6` | analysis_hn |
| 广东 | GD | `gd_21b1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5` | analysis_gd |
| 广西 | GX | `gx_10a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4` | analysis_gx |
| 海南 | HI | `hi_0f9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3` | analysis_hi |
| 重庆 | CQ | `cq_fe8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2` | analysis_cq |
| 四川 | SC | `sc_ed7da0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1` | analysis_sc |
| 云南 | YN | `yn_dc6c9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0` | analysis_yn |
| 西藏 | XZ | `xz_cb5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9` | analysis_xz |
| 陕西 | SN | `sn_ba4a7da0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8` | analysis_sn |
| 青海 | QH | `qh_a939c6c9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7` | analysis_qh |
| 新疆 | XJ | `xj_9828b5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6` | analysis_xj |

## 🚀 使用方法

### 1. HTTP请求示例

```bash
# 访问贵州数据库
curl -H "Authorization: Bearer gz_6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4" \
     "http://localhost:8002/api/statistics"

# 访问北京数据库
curl -H "Authorization: Bearer bj_3e8b1c4f7a0d9c2f5b8e1a4d7c0f3b6e9a2d5c8" \
     "http://localhost:8002/export/statistics?rpt_month=202301"

# 访问总部数据库
curl -H "Authorization: Bearer jt_4f9c2b5e8a1d7c0f3b6e9a2d5c8f1b4e7a0d3c6" \
     "http://localhost:8002/export/details?category=其他费用"
```

### 2. JavaScript示例

```javascript
// 访问上海数据库
const response = await fetch('http://localhost:8002/api/statistics', {
    headers: {
        'Authorization': 'Bearer sh_c6f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5'
    }
});

const data = await response.json();
console.log('上海数据:', data);
```

### 3. Python示例

```python
import requests

# 访问广东数据库
headers = {
    'Authorization': 'Bearer gd_21b1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5'
}

response = requests.get(
    'http://localhost:8002/api/statistics',
    headers=headers
)

if response.status_code == 200:
    data = response.json()
    print(f"广东数据库: {data['database']}")
    print(f"数据总量: {data['total']}")
```

## 🛡️ 安全特性

1. **数据隔离**: 每个Token只能访问对应省份的数据库
2. **权限控制**: 用户无法通过URL参数修改数据库
3. **审计追踪**: 可以通过Token前缀识别访问来源
4. **防止泄露**: Token包含省份标识，便于管理和监控

## 📊 API响应

API响应中会包含当前使用的数据库信息：

```json
{
    "total": 162124,
    "database": "analysis_gz",
    "categories": [...],
    "filters": {...}
}
```

## 🔧 环境变量配置

### 动态数据库配置

数据库名称从环境变量`PROVINCE_DATABASE_MAPPING`读取，格式为：
```
省份代码:数据库名称:省份名称,省份代码:数据库名称:省份名称,...
```

**修改数据库名称示例**：
```bash
# 原配置
PROVINCE_DATABASE_MAPPING=XJ:analysis_xj:新疆,GZ:analysis_gz:贵州,...

# 修改新疆数据库名称
PROVINCE_DATABASE_MAPPING=XJ:analysis_xjj:新疆,GZ:analysis_gz:贵州,...
```

修改后，新疆Token `xj_9828b5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6` 将自动访问 `analysis_xjj` 数据库。

### 自定义Token配置

如需添加自定义Token，可以设置环境变量：

```bash
export EXPENSE_API_TOKEN_DB=custom_token:custom_database
```

## ⚠️ 注意事项

1. Token区分大小写，请确保完全匹配
2. 每个Token只能访问对应的数据库，无法跨省份访问
3. 导出的文件名会自动包含省份标识
4. 如果数据库不存在，会返回相应的错误信息

## 🆘 常见问题

**Q: 如何知道我的Token对应哪个数据库？**
A: Token前缀就是省份代码，如`gz_`对应贵州，`bj_`对应北京。具体数据库名称从环境变量动态读取。

**Q: 可以用一个Token访问多个省份的数据吗？**
A: 不可以，每个Token只能访问对应的一个数据库，这是安全设计

**Q: 忘记了某个省份的Token怎么办？**
A: 请参考上面的映射表，或联系系统管理员

**Q: 如何修改某个省份的数据库名称？**
A: 修改环境变量`PROVINCE_DATABASE_MAPPING`中对应省份的数据库名称，重启API服务后生效

**Q: 修改数据库名称后Token会变吗？**
A: Token不会变，只是Token对应的数据库会变。例如新疆Token始终是`xj_9828...`，但可以指向不同的数据库
