#!/usr/bin/env python3
"""
一键重装脚本
清空当前环境的所有 pip 包，然后重新安装 requirements.txt
"""

import subprocess
import sys
import os

def run_command(command: str, ignore_errors: bool = False):
    """执行命令"""
    print(f"🔧 执行: {command}")
    try:
        result = subprocess.run(command, shell=True, check=not ignore_errors, text=True)
        if result.returncode == 0:
            print("   ✅ 成功")
        else:
            print("   ⚠️ 有警告或错误（继续执行）")
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 失败: {e}")
        if not ignore_errors:
            raise
        return False

def backup_current_environment():
    """备份当前环境"""
    print("📦 备份当前环境...")
    return run_command("pip freeze > backup_before_reinstall.txt", ignore_errors=True)

def uninstall_all_packages():
    """卸载所有用户安装的包"""
    print("🗑️ 获取已安装的包列表...")
    
    # 获取所有已安装的包（排除基础包）
    try:
        result = subprocess.run("pip freeze", shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 无法获取包列表")
            return False
            
        packages = result.stdout.strip().split('\n')
        packages = [pkg.split('==')[0] for pkg in packages if pkg and not pkg.startswith('-')]
        
        # 排除一些基础包，避免破坏环境
        excluded_packages = {'pip', 'setuptools', 'wheel', 'packaging'}
        packages = [pkg for pkg in packages if pkg.lower() not in excluded_packages]
        
        if not packages:
            print("✅ 没有需要卸载的包")
            return True
            
        print(f"📝 找到 {len(packages)} 个需要卸载的包")
        
        # 批量卸载
        packages_str = ' '.join(packages)
        print(f"🗑️ 卸载所有包...")
        return run_command(f"pip uninstall {packages_str} -y", ignore_errors=True)
        
    except Exception as e:
        print(f"❌ 卸载过程出错: {e}")
        return False

def install_new_requirements():
    """安装新的 requirements.txt"""
    print("📦 安装新的 requirements.txt...")
    
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt 文件不存在！")
        return False
    
    # 先更新 pip
    print("⬆️ 更新 pip...")
    run_command("python -m pip install --upgrade pip", ignore_errors=True)
    
    # 安装新的依赖
    print("📋 安装新依赖...")
    return run_command("pip install -r requirements.txt")

def verify_installation():
    """验证安装结果"""
    print("🧪 验证安装结果...")
    
    # 基本验证
    critical_packages = [
        "langchain",
        "langchain_openai", 
        "openai",
        "numpy",
        "streamlit",
        "fastapi"
    ]
    
    success_count = 0
    for package in critical_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
            success_count += 1
        except ImportError:
            print(f"   ❌ {package} 导入失败")
    
    print(f"\n📊 验证结果: {success_count}/{len(critical_packages)} 个核心包正常")
    return success_count == len(critical_packages)

def main():
    """主函数"""
    print("🔄 开始一键重装流程...")
    print("=" * 60)
    print("⚠️ 警告：这将删除当前环境中的所有 pip 包！")
    
    # 确认操作
    response = input("\n❓ 确定要继续吗？(输入 'YES' 确认): ")
    if response != 'YES':
        print("❌ 用户取消操作")
        return False
    
    try:
        # 步骤 1: 备份
        print("\n📦 步骤 1: 备份当前环境")
        backup_current_environment()
        
        # 步骤 2: 卸载所有包
        print("\n🗑️ 步骤 2: 卸载所有包")
        if not uninstall_all_packages():
            print("⚠️ 卸载过程有警告，但继续安装...")
        
        # 步骤 3: 安装新依赖
        print("\n📦 步骤 3: 安装新依赖")
        if not install_new_requirements():
            print("❌ 安装新依赖失败！")
            print("\n🔧 恢复建议:")
            print("   pip install -r backup_before_reinstall.txt")
            return False
        
        # 步骤 4: 验证
        print("\n🧪 步骤 4: 验证安装")
        if verify_installation():
            print("\n🎉 重装成功！所有核心包都正常工作")
            print("✨ 新的 requirements.txt 已成功安装")
            return True
        else:
            print("\n⚠️ 重装完成，但验证发现一些问题")
            print("   请手动检查具体的包导入情况")
            return False
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 操作被用户中断")
        print("🔧 如需恢复：pip install -r backup_before_reinstall.txt")
        return False
    except Exception as e:
        print(f"\n\n💥 重装过程中发生错误: {e}")
        print("\n🔧 恢复建议:")
        print("1. pip install -r backup_before_reinstall.txt")
        print("2. 检查 Python 环境是否正常")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎯 重装完成！现在可以正常使用项目了")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
        sys.exit(1) 