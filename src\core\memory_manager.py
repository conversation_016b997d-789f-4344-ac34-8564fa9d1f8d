"""
记忆管理器
支持多种记忆存储方式，包括内存、Redis、MongoDB等
"""

from typing import Dict, List, Any, Optional
from langchain.memory import (
    ConversationBufferMemory,
    ConversationBufferWindowMemory,
    ConversationSummaryMemory,
    ConversationSummaryBufferMemory
)
from langchain_community.chat_message_histories import (
    ChatMessageHistory,
    RedisChatMessageHistory,
    MongoDBChatMessageHistory
)
from langchain_core.language_models.base import BaseLanguageModel

from ..config import settings


class MemoryManager:
    """记忆管理器"""
    
    @staticmethod
    def create_memory(
        memory_type: str = "buffer",
        session_id: str = "default",
        storage_type: str = "memory",
        llm: Optional[BaseLanguageModel] = None,
        **kwargs
    ):
        """
        创建记忆实例
        
        Args:
            memory_type: 记忆类型 (buffer, window, summary, summary_buffer)
            session_id: 会话ID
            storage_type: 存储类型 (memory, redis, mongodb)
            llm: 语言模型（用于摘要类型记忆）
            **kwargs: 其他参数
            
        Returns:
            记忆实例
        """
        # 创建消息历史
        chat_history = MemoryManager._create_chat_history(storage_type, session_id)
        
        # 根据记忆类型创建对应的记忆实例
        if memory_type == "buffer":
            return ConversationBufferMemory(
                chat_memory=chat_history,
                return_messages=True,
                **kwargs
            )
        elif memory_type == "window":
            k = kwargs.pop("k", 10)
            return ConversationBufferWindowMemory(
                chat_memory=chat_history,
                k=k,
                return_messages=True,
                **kwargs
            )
        elif memory_type == "summary":
            if not llm:
                raise ValueError("摘要记忆需要提供语言模型")
            return ConversationSummaryMemory(
                chat_memory=chat_history,
                llm=llm,
                return_messages=True,
                **kwargs
            )
        elif memory_type == "summary_buffer":
            if not llm:
                raise ValueError("摘要缓冲记忆需要提供语言模型")
            max_token_limit = kwargs.pop("max_token_limit", 2000)
            return ConversationSummaryBufferMemory(
                chat_memory=chat_history,
                llm=llm,
                max_token_limit=max_token_limit,
                return_messages=True,
                **kwargs
            )
        else:
            raise ValueError(f"不支持的记忆类型: {memory_type}")
    
    @staticmethod
    def _create_chat_history(storage_type: str, session_id: str):
        """创建聊天历史存储"""
        if storage_type == "memory":
            return ChatMessageHistory()
        elif storage_type == "redis":
            return RedisChatMessageHistory(
                session_id=session_id,
                url=settings.redis_url
            )
        elif storage_type == "mongodb":
            return MongoDBChatMessageHistory(
                connection_string=settings.mongodb_url,
                session_id=session_id,
                database_name=settings.mongodb_db_name
            )
        else:
            raise ValueError(f"不支持的存储类型: {storage_type}")
    
    @staticmethod
    def get_memory_info(memory) -> Dict[str, Any]:
        """获取记忆信息"""
        info = {
            "type": type(memory).__name__,
            "messages_count": len(memory.chat_memory.messages) if hasattr(memory, 'chat_memory') else 0
        }
        
        # 添加特定类型的信息
        if hasattr(memory, 'k'):
            info["window_size"] = memory.k
        if hasattr(memory, 'max_token_limit'):
            info["max_token_limit"] = memory.max_token_limit
            
        return info 