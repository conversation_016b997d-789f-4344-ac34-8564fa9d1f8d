"""
数据库配置模块
"""
import os
from typing import Dict, Any
import pymysql
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        self.host = os.getenv('MYSQL_HOST', '**********')
        self.port = int(os.getenv('MYSQL_PORT', 8066))
        self.user = os.getenv('MYSQL_USER', 'root')
        self.password = os.getenv('MYSQL_PASSWORD', 'Q.-analysis@#/tidb')
        self.database = os.getenv('MYSQL_DATABASE', 'analysis_gz')
        self.charset = os.getenv('MYSQL_CHARSET', 'utf8mb4')
    
    def get_connection_string(self) -> str:
        """获取数据库连接字符串"""
        return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    def get_pymysql_config(self) -> Dict[str, Any]:
        """获取PyMySQL连接配置"""
        return {
            'host': self.host,
            'port': self.port,
            'user': self.user,
            'password': self.password,
            'database': self.database,
            'charset': self.charset,
            'autocommit': True
        }
    
    def create_engine(self):
        """创建SQLAlchemy引擎"""
        return create_engine(
            self.get_connection_string(),
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False
        )
    
    def create_session(self):
        """创建数据库会话"""
        engine = self.create_engine()
        Session = sessionmaker(bind=engine)
        return Session()

# 全局数据库配置实例
db_config = DatabaseConfig()
