# Analysis Agent 综合使用指南

## 📋 项目概述

Analysis Agent 是一个基于LangChain的智能数据分析系统，集成了多种语言模型、向量数据库和工具调用能力，专门用于自然语言到SQL查询的智能转换和数据分析。

### ✨ 核心功能

- 🤖 **智能SQL生成**: 自然语言转SQL查询
- 🏠 **本地模型支持**: 支持本地部署的LLM（如Qwen系列）
- 📊 **多数据库支持**: 支持MySQL、PostgreSQL等多种数据库
- 🌍 **省份感知查询**: 自动识别省份，选择对应数据库
- 🔍 **向量增强检索**: 基于ChromaDB的复杂提示词匹配
- 🌐 **OpenAI兼容API**: 可直接在Dify等平台使用
- 🛠️ **工具调用能力**: 支持多种工具的智能调用
- ⚙️ **灵活配置**: 支持环境变量和命令行参数配置

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd analysis-agent

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

复制环境变量模板并配置：

```bash
# Windows
copy env.example .env

# Linux/Mac
cp env.example .env
```

编辑 `.env` 文件，配置关键参数：

```bash
# ================================
# 本地LLM配置
# ================================
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_api_key_here
LOCAL_LLM_MODEL_URL=http://your_model_url/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B
LOCAL_LLM_TIMEOUT=60

# ================================
# Agent配置
# ================================
AGENT_MAX_ITERATIONS=10    # 最大工具调用次数
AGENT_TIMEOUT=300          # Agent超时时间（秒）

# ================================
# 服务器配置
# ================================
SERVER_HOST=0.0.0.0        # 服务器监听地址
SERVER_PORT=8001           # 服务器端口

# ================================
# 数据库配置
# ================================
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database

# ================================
# 省份数据库映射
# ================================
PROVINCE_DATABASE_MAPPING=GZ:analysis_gz:贵州,BJ:analysis_bj:北京,SH:analysis_sh:上海
```

### 3. 启动服务

#### 方式1：使用启动脚本（推荐）

```bash
# 查看当前配置
python start_analysis_agent_server.py --show-config

# 使用默认配置启动
python start_analysis_agent_server.py

# 指定端口启动
python start_analysis_agent_server.py --port 8888

# 指定主机和端口
python start_analysis_agent_server.py --host 127.0.0.1 --port 8888
```

#### 方式2：直接启动

```bash
python examples/analysis_agent_server.py --mode server --host 0.0.0.0 --port 8001
```

## 📊 配置说明

### 核心配置项

| 配置项 | 环境变量 | 默认值 | 说明 |
|--------|----------|--------|------|
| 服务器地址 | `SERVER_HOST` | `0.0.0.0` | 服务器监听的IP地址 |
| 服务器端口 | `SERVER_PORT` | `8001` | 服务器监听的端口 |
| 最大迭代次数 | `AGENT_MAX_ITERATIONS` | `10` | Agent最多可以调用工具的次数 |
| Agent超时 | `AGENT_TIMEOUT` | `300` | Agent处理请求的超时时间（秒） |
| LLM超时 | `LOCAL_LLM_TIMEOUT` | `60` | 单次LLM请求的超时时间（秒） |

### 配置优先级

1. **命令行参数**（最高优先级）
2. **环境变量**（`.env` 文件）
3. **代码默认值**（最低优先级）

## 🔧 API使用指南

### 1. 健康检查

```bash
GET http://localhost:8001/health
```

### 2. SQL查询接口

```bash
POST http://localhost:8001/v1/sql/query
Content-Type: application/json

{
    "question": "2025年4月的电费是多少？",
    "session_id": "optional_session_id",
    "include_sql": true,
    "include_data": true
}
```

### 3. OpenAI兼容接口

```bash
POST http://localhost:8001/v1/chat/completions
Content-Type: application/json
Authorization: Bearer any_key

{
    "model": "sql-agent",
    "messages": [
        {
            "role": "user",
            "content": "查询最近的电费数据"
        }
    ],
    "stream": false
}
```

### 4. 省份感知查询

```bash
POST http://localhost:8001/v1/sql/query
Content-Type: application/json

{
    "question": "贵州省2025年4月的电费是多少？",
    "system": "省份代码：GZ"
}
```

## 🌍 省份数据库映射

系统支持根据省份代码自动选择对应的数据库：

### 支持的省份代码

| 省份代码 | 数据库名称 | 省份名称 |
|---------|-----------|----------|
| GZ | analysis_gz | 贵州 |
| BJ | analysis_bj | 北京 |
| SH | analysis_sh | 上海 |
| GD | analysis_gd | 广东 |
| ... | ... | ... |

### 使用方式

1. **在system消息中指定省份代码**：
   ```json
   {
       "question": "电费查询",
       "system": "省份代码：GZ"
   }
   ```

2. **在问题中包含省份信息**：
   ```json
   {
       "question": "贵州省的电费情况如何？"
   }
   ```

## 🔍 向量增强系统

系统集成了基于ChromaDB的向量检索功能，支持复杂提示词的智能匹配。

### 工作流程

```
用户问题: "当前电费情况如何"
    ↓
1. 检索向量数据库 (ChromaDB)
    ↓
2. 匹配到"电费情况综合分析"提示词
    ↓
3. 执行复杂处理步骤:
   - 查询最近3个月电费数据
   - 分析电费构成和趋势
   - 对比历史数据
   - 提供优化建议
    ↓
4. 按预定格式返回结构化报告
```

### 管理界面

访问 `http://localhost:8001/admin` 可以：
- 查看和管理复杂提示词
- 添加新的提示词模板
- 测试向量检索效果

## 🔌 Dify集成

### 配置步骤

1. **在Dify中添加自定义模型提供商**：
   - 模型提供商：自定义
   - API Base URL：`http://localhost:8001/v1`
   - API Key：任意字符串
   - 模型名称：`sql-agent`

2. **测试连接**：
   - 发送测试消息验证连接
   - 确认返回正常的SQL查询结果

### 支持的功能

- ✅ 聊天完成接口 (`/v1/chat/completions`)
- ✅ 模型列表接口 (`/v1/models`)
- ✅ 流式和非流式响应
- ✅ 跨域支持 (CORS)

## 🛠️ 工具调用能力

系统支持多种工具的智能调用：

### 可用工具

1. **integrated_sql**: SQL查询工具
2. **knowledge_search**: 知识库搜索工具
3. **database_switch**: 数据库切换工具

### 工具调用流程

1. 用户提出问题
2. Agent分析问题类型
3. 选择合适的工具
4. 执行工具调用
5. 处理工具返回结果
6. 生成最终回答

## 📈 性能优化

### 配置建议

- **开发环境**：
  ```bash
  AGENT_TIMEOUT=120
  AGENT_MAX_ITERATIONS=5
  ```

- **生产环境**：
  ```bash
  AGENT_TIMEOUT=300
  AGENT_MAX_ITERATIONS=10
  ```

### 监控和调试

- 访问 `http://localhost:8001/stats` 查看性能统计
- 访问 `http://localhost:8001/docs` 查看API文档
- 查看日志文件 `logs/analysis_agent_*.log`

## 🚨 常见问题

### 1. 端口被占用

```bash
# 使用其他端口启动
python start_analysis_agent_server.py --port 8888
```

### 2. 数据库连接失败

检查 `.env` 文件中的数据库配置：
```bash
MYSQL_HOST=correct_host
MYSQL_PORT=correct_port
MYSQL_USER=correct_user
MYSQL_PASSWORD=correct_password
```

### 3. LLM连接超时

调整超时设置：
```bash
LOCAL_LLM_TIMEOUT=120
AGENT_TIMEOUT=600
```

### 4. 依赖安装问题

```bash
# 分步安装
pip install langchain langchain-openai langchain-community
pip install fastapi uvicorn
pip install chromadb faiss-cpu
```

## 📚 更多资源

- **API文档**: `http://localhost:8001/docs`
- **管理界面**: `http://localhost:8001/admin`
- **性能统计**: `http://localhost:8001/stats`
- **健康检查**: `http://localhost:8001/health`

## 💡 最佳实践

1. **配置管理**: 使用 `.env` 文件管理配置，避免硬编码
2. **性能调优**: 根据实际需求调整超时时间和迭代次数
3. **监控运维**: 定期检查日志和性能统计
4. **安全考虑**: 在生产环境中使用适当的认证和授权机制
