#!/usr/bin/env python3
"""
简化工具名称示例
展示使用简洁工具名称的复杂提示词
"""

import sys
import os
import uuid
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager, ComplexPrompt

def create_simplified_tool_examples():
    """创建使用简化工具名称的示例提示词"""
    print("🔧 创建简化工具名称示例")
    print("=" * 60)
    
    try:
        manager = get_prompt_vectorstore_manager()
        
        # 示例1：您的实际需求（使用简化工具名称）
        prompt1 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="指标环比排名综合评分（简化版）",
            description="使用简化工具名称进行指标评分分析",
            trigger_questions=[
                "指标环比排名评分简化版",
                "简化版综合评分分析",
                "环比排名评分优化版"
            ],
            processing_steps=[
                "[SQL查询:JT:当前电费情况如何] 获取全国电费数据和排名",
                "[变量:查询结果=步骤1结果] 保存查询数据",
                """[计算:综合评分] 根据查询结果进行综合评分：
                - 环比变动权重70%，全国排名权重30%
                - 基准分50分，低于50分为劣化
                - 环比：下降1%加1分，上升1%减1分（小数四舍五入）
                - 排名：上升1名加5分，下降1名减5分
                - 示例：环比上升3%，排名下降2名
                  环比分=50-3=47，排名分=50-10=40
                  综合分=47×70%+40×30%=44.9分""",
                "[计算:劣化识别] 如所有指标未劣化，则标记全国排名最低指标为劣化",
                "[调用工具:知识:劣化指标的改进建议] 获取改进方案",
                "[变量:改进建议=工具结果] 保存改进建议",
                "生成综合评分分析报告"
            ],
            response_format="""
# 📊 指标环比排名综合评分报告

## 📈 数据概览
[引用:查询结果]

## 🧮 评分详情
[引用:综合评分结果]

## ⚠️ 劣化指标
[引用:劣化识别结果]

## 💡 改进建议
[引用:改进建议]

## 📋 评分说明
- 环比变动权重：70%
- 全国排名权重：30%
- 劣化标准：综合得分 < 50分
            """,
            priority=4,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例2：多工具调用示例
        prompt2 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="多工具协作分析示例",
            description="展示多种简化工具名称的协作使用",
            trigger_questions=[
                "多工具协作分析",
                "简化工具协作示例",
                "工具链分析流程"
            ],
            processing_steps=[
                "[SQL查询:原始:电费基础数据] 获取本省电费数据",
                "[SQL查询:JT:全国电费对比数据] 获取全国对比数据",
                "[调用工具:搜索:电费优化最佳实践] 搜索优化方案",
                "[调用工具:问答:如何降低电费成本] 获取专业建议",
                "[调用工具:kb:电费管理经验] 查询知识库",
                "[计算:综合分析] 整合所有数据和建议",
                "生成多维度分析报告"
            ],
            response_format="""
# 📊 多工具协作分析报告

## 📈 数据分析
- 本省数据：[引用:本省数据]
- 全国对比：[引用:全国数据]

## 💡 优化建议
- 最佳实践：[引用:搜索结果]
- 专业建议：[引用:问答结果]
- 经验总结：[引用:知识库结果]

## 🎯 综合方案
[引用:综合分析结果]
            """,
            priority=3,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 示例3：工具名称对比示例
        prompt3 = ComplexPrompt(
            id=str(uuid.uuid4()),
            title="工具名称对比示例",
            description="展示原始名称vs简化名称的对比",
            trigger_questions=[
                "工具名称对比示例",
                "简化前后对比",
                "工具别名演示"
            ],
            processing_steps=[
                "# 原始写法（较长）：",
                "[调用工具:knowledge_search:电费优化建议] 使用完整工具名",
                "",
                "# 简化写法（推荐）：",
                "[调用工具:知识:电费优化建议] 使用简化别名",
                "[调用工具:搜索:成本控制方案] 使用搜索别名", 
                "[调用工具:问答:如何提高效率] 使用问答别名",
                "[调用工具:kb:最佳实践] 使用kb别名",
                "[调用工具:ask:专业建议] 使用ask别名",
                "",
                "生成对比分析报告"
            ],
            response_format="""
# 📊 工具名称使用对比

## 📝 支持的简化别名
- 知识 → knowledge_search
- 搜索 → knowledge_search  
- 问答 → knowledge_search
- kb → knowledge_search
- ask → knowledge_search

## 💡 使用建议
推荐使用简化别名，更容易记忆和输入
            """,
            priority=2,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 添加示例提示词
        prompts = [prompt1, prompt2, prompt3]
        success_count = 0
        
        for prompt in prompts:
            if manager.add_complex_prompt(prompt):
                success_count += 1
                print(f"✅ 成功添加示例提示词: {prompt.title}")
            else:
                print(f"❌ 添加示例提示词失败: {prompt.title}")
        
        print(f"\n📊 总结: 成功添加 {success_count}/{len(prompts)} 个示例提示词")
        
        if success_count > 0:
            print("\n🎯 简化工具名称说明:")
            print("📝 知识搜索工具的简化别名:")
            print("  - 知识 → knowledge_search")
            print("  - 搜索 → knowledge_search")
            print("  - 问答 → knowledge_search")
            print("  - kb → knowledge_search")
            print("  - ask → knowledge_search")
            
            print("\n💡 使用示例:")
            print("  原始: [调用工具:knowledge_search:劣化指标改进建议]")
            print("  简化: [调用工具:知识:劣化指标改进建议]")
            print("  更简: [调用工具:kb:劣化指标改进建议]")
            
            print("\n✅ 优势:")
            print("  - 更容易记忆")
            print("  - 输入更快捷")
            print("  - 减少拼写错误")
            print("  - 支持中英文混用")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 创建示例失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_tool_mapping_guide():
    """显示工具映射指南"""
    print("\n📋 工具名称映射指南")
    print("=" * 60)
    
    print("🎯 支持的工具别名:")
    
    print("\n📚 知识搜索工具 (knowledge_search):")
    print("  - 知识   [调用工具:知识:查询内容]")
    print("  - 搜索   [调用工具:搜索:查询内容]")
    print("  - 问答   [调用工具:问答:查询内容]")
    print("  - 查询   [调用工具:查询:查询内容]")
    print("  - kb     [调用工具:kb:查询内容]")
    print("  - ask    [调用工具:ask:查询内容]")
    
    print("\n💾 SQL工具 (integrated_sql):")
    print("  - sql    [调用工具:sql:查询内容]")
    print("  - SQL    [调用工具:SQL:查询内容]")
    print("  - 数据库  [调用工具:数据库:查询内容]")
    print("  注：SQL查询推荐使用专门语法 [SQL查询:数据库:内容]")
    
    print("\n🔧 使用建议:")
    print("  1. 优先使用中文别名（知识、搜索、问答）")
    print("  2. 英文别名适合快速输入（kb、ask）")
    print("  3. 保持团队内命名一致性")
    print("  4. 可以混合使用不同别名")

if __name__ == "__main__":
    print("🚀 简化工具名称示例")
    print("=" * 60)
    
    if create_simplified_tool_examples():
        show_tool_mapping_guide()
        
        print("\n" + "=" * 60)
        print("🎉 简化工具名称示例创建完成！")
        print("\n💡 现在您可以使用:")
        print("  [调用工具:知识:劣化指标改进建议]")
        print("  [调用工具:搜索:电费优化方案]")
        print("  [调用工具:kb:最佳实践]")
        print("\n而不需要记住完整的 knowledge_search ！")
    else:
        print("\n❌ 示例创建失败，请检查配置")
