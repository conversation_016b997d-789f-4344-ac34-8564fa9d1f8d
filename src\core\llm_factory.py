"""
语言模型工厂类
支持OpenAI、Anthropic、本地模型等多种模型提供商
"""

from typing import Optional, Dict, Any
import requests
import json
import numpy as np
import re
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from langchain_anthropic import ChatAnthropic
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.embeddings import Embeddings
from langchain_core.language_models.llms import LLM
from langchain_core.callbacks.manager import CallbackManagerForLLMRun

from ..config import settings


class LocalLLM(LLM):
    """本地大语言模型实现"""
    
    def __init__(self, api_key: str, model_url: str, model_name: str, 
                 preserve_think_tags: bool = True, **kwargs):
        super().__init__(**kwargs)
        self._api_key = api_key
        self._model_url = model_url
        self._model_name = model_name
        self._preserve_think_tags = preserve_think_tags
        self._temperature = kwargs.get("temperature", 0.7)
        self._timeout = kwargs.get("timeout", 60)
    
    @property
    def api_key(self):
        return self._api_key
    
    @property
    def model_url(self):
        return self._model_url
    
    @property
    def model_name(self):
        return self._model_name
    
    @property
    def temperature(self):
        return self._temperature
    
    @property
    def timeout(self):
        return self._timeout
    
    @property
    def _llm_type(self) -> str:
        return "local_llm"
    
    def _call(
        self,
        prompt: str,
        stop: Optional[list] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """调用本地LLM"""
        # 构建消息格式 - 根据配置决定是否添加/no_think指令
        #messages = [{"role": "user", "content": prompt + " /no_think"}]
        if self._preserve_think_tags:
            # 保留think标签，不添加/no_think指令
            messages = [{"role": "user", "content": prompt}]
        else:
            # 删除think标签，添加/no_think指令
            messages = [{"role": "user", "content": prompt + " /no_think"}]
        
        # 准备请求头
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        }
        
        # 准备请求体
        data = {
            'model': self.model_name,
            'stream': False,
            'messages': messages,
            'temperature': kwargs.get('temperature', self.temperature)
        }
        
        try:
            # 发送请求
            response = requests.post(
                self.model_url,
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 检查响应格式
            if not result.get('choices') or len(result['choices']) == 0:
                raise ValueError("Invalid response format from API")
            
            # 获取响应内容
            content = result['choices'][0].get('message', {}).get('content')
            if content is None:
                raise ValueError("No content in API response")
                
            # 处理返回结果中的think标签
            def remove_think_tags(text):
                try:
                    # 移除<think>标签
                    cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
                    return cleaned_text.strip() if cleaned_text.strip() else text
                except Exception:
                    return text

            # 处理返回内容 - 根据配置决定是否保留think标签
            if self._preserve_think_tags:
                return content  # 保留think标签
            else:
                cleaned_content = remove_think_tags(content)
                return cleaned_content
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"API request failed: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("Failed to parse API response")
        except Exception as e:
            raise Exception(f"Error in local LLM call: {str(e)}")


class RemoteBGEEmbeddings(Embeddings):
    """远程BGE嵌入模型实现"""
    
    def __init__(self, api_url: str, api_key: Optional[str] = None, 
                 model_name: str = "bge-large-zh-v1.5", timeout: int = 30):
        self.api_url = api_url
        self.api_key = api_key
        self.model_name = model_name
        self.timeout = timeout
    
    def embed_documents(self, texts):
        """嵌入文档列表"""
        return self._get_embeddings(texts)
    
    def embed_query(self, text: str):
        """嵌入单个查询"""
        embeddings = self._get_embeddings([text])
        return embeddings[0] if embeddings else []
    
    def _get_embeddings(self, texts):
        """调用远程BGE模型获取embedding向量"""
        try:
            # 准备请求头
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # 如果有API密钥，添加到请求头
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            # 准备请求数据 - 使用Xinference/OpenAI兼容格式
            data = {
                'model': self.model_name,
                'input': texts
            }
            
            # 发送请求
            response = requests.post(
                self.api_url,
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 提取embedding向量 - 兼容Xinference/OpenAI格式
            embeddings = []
            if 'data' in result:
                # OpenAI/Xinference格式: {"data": [{"embedding": [向量]}]}
                for item in result['data']:
                    if 'embedding' in item:
                        embeddings.append(item['embedding'])
            elif 'embeddings' in result:
                # 备用格式: {"embeddings": [[向量1], [向量2]]}
                embeddings = result['embeddings']
            else:
                # 如果响应格式不符合预期，尝试直接使用结果
                embeddings = result
            
            # 确保返回的是列表
            if isinstance(embeddings, list) and len(embeddings) > 0:
                return embeddings
            else:
                print(f"⚠️ 意外的BGE API响应格式: {result}")
                # 返回零向量作为备选
                return [np.zeros(1024).tolist() for _ in texts]
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 远程BGE API请求失败: {e}")
            # 返回零向量作为备选
            return [np.zeros(1024).tolist() for _ in texts]
        except Exception as e:
            print(f"❌ 远程BGE embedding处理失败: {e}")
            # 返回零向量作为备选
            return [np.zeros(1024).tolist() for _ in texts]


class LLMFactory:
    """语言模型工厂类"""
    
    @staticmethod
    def create_chat_model(
        provider: str = "openai",
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        preserve_think_tags: bool = True,  # 添加此参数，true保留think标签，false不保留
        **kwargs
    ) -> BaseLanguageModel:
        """
        创建聊天模型
        
        Args:
            provider: 模型提供商 (openai, anthropic, local)
            model_name: 模型名称
            temperature: 温度参数
            preserve_think_tags: 是否保留think标签（仅对local模型有效）
            **kwargs: 其他参数
            
        Returns:
            BaseLanguageModel: 语言模型实例
        """
        if provider.lower() == "openai":
            return LLMFactory._create_openai_model(model_name, temperature, **kwargs)
        elif provider.lower() == "anthropic":
            return LLMFactory._create_anthropic_model(model_name, temperature, **kwargs)
        elif provider.lower() == "local":
            return LLMFactory._create_local_model(model_name, temperature, preserve_think_tags, **kwargs)
        else:
            raise ValueError(f"不支持的模型提供商: {provider}")
    
    @staticmethod
    def _create_openai_model(
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        **kwargs
    ) -> ChatOpenAI:
        """创建OpenAI模型"""
        if not settings.openai_api_key:
            raise ValueError("未设置OpenAI API密钥，请检查环境变量OPENAI_API_KEY")
        
        default_model = model_name or "gpt-3.5-turbo"
        
        return ChatOpenAI(
            model=default_model,
            temperature=temperature,
            openai_api_key=settings.openai_api_key,
            openai_api_base=settings.openai_api_base,
            **kwargs
        )
    
    @staticmethod
    def _create_anthropic_model(
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        **kwargs
    ) -> ChatAnthropic:
        """创建Anthropic模型"""
        if not settings.anthropic_api_key:
            raise ValueError("未设置Anthropic API密钥，请检查环境变量ANTHROPIC_API_KEY")
        
        default_model = model_name or "claude-3-haiku-20240307"
        
        return ChatAnthropic(
            model=default_model,
            temperature=temperature,
            anthropic_api_key=settings.anthropic_api_key,
            **kwargs
        )
    
    @staticmethod
    def _create_local_model(
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        preserve_think_tags: bool = True,  # 添加此参数，默认保留think标签
        **kwargs
    ) -> LocalLLM:
        """创建本地模型"""
        if not settings.local_llm_enabled:
            raise ValueError("本地LLM未启用，请设置LOCAL_LLM_ENABLED=true")
        
        if not settings.local_llm_api_key:
            raise ValueError("未设置本地LLM API密钥，请检查环境变量LOCAL_LLM_API_KEY")
        
        if not settings.local_llm_model_url:
            raise ValueError("未设置本地LLM URL，请检查环境变量LOCAL_LLM_MODEL_URL")
        
        if not settings.local_llm_model_name:
            raise ValueError("未设置本地LLM模型名称，请检查环境变量LOCAL_LLM_MODEL_NAME")
        
        # 🔧 修复：从kwargs中提取timeout参数，避免重复传递
        timeout = kwargs.pop('timeout', settings.local_llm_timeout)
        
        return LocalLLM(
            api_key=settings.local_llm_api_key,
            model_url=settings.local_llm_model_url,
            model_name=model_name or settings.local_llm_model_name,
            temperature=temperature,
            preserve_think_tags=preserve_think_tags,  # 传递参数
            timeout=timeout,
            **kwargs
        )
    
    @staticmethod
    def create_embeddings(
        provider: str = "openai",
        model_name: Optional[str] = None,
        **kwargs
    ) -> Embeddings:
        """
        创建嵌入模型
        
        Args:
            provider: 模型提供商 (openai, local_bge)
            model_name: 模型名称
            **kwargs: 其他参数
            
        Returns:
            Embeddings: 嵌入模型实例
        """
        if provider.lower() == "openai":
            return LLMFactory._create_openai_embeddings(model_name, **kwargs)
        elif provider.lower() == "local_bge":
            return LLMFactory._create_local_bge_embeddings(model_name, **kwargs)
        else:
            raise ValueError(f"不支持的嵌入模型提供商: {provider}")
    
    @staticmethod
    def _create_openai_embeddings(
        model_name: Optional[str] = None,
        **kwargs
    ) -> OpenAIEmbeddings:
        """创建OpenAI嵌入模型"""
        if not settings.openai_api_key:
            raise ValueError("未设置OpenAI API密钥，请检查环境变量OPENAI_API_KEY")
        
        default_model = model_name or "text-embedding-ada-002"
        
        return OpenAIEmbeddings(
            model=default_model,
            openai_api_key=settings.openai_api_key,
            openai_api_base=settings.openai_api_base,
            **kwargs
        )
    
    @staticmethod
    def _create_local_bge_embeddings(
        model_name: Optional[str] = None,
        **kwargs
    ) -> RemoteBGEEmbeddings:
        """创建本地BGE嵌入模型"""
        if not settings.local_bge_enabled:
            raise ValueError("本地BGE未启用，请设置LOCAL_BGE_ENABLED=true")
        
        if not settings.local_bge_api_url:
            raise ValueError("未设置本地BGE API URL，请检查环境变量LOCAL_BGE_API_URL")
        
        # 首先尝试主服务器
        try:
            embeddings = RemoteBGEEmbeddings(
                api_url=settings.local_bge_api_url,
                api_key=settings.local_bge_api_key,
                model_name=model_name or settings.local_bge_model_name,
                timeout=settings.local_bge_timeout
            )
            
            # 测试连接
            print("🔍 测试本地BGE服务连接...")
            test_result = embeddings.embed_query("测试连接")
            if test_result and len(test_result) > 0:
                print("✅ 本地BGE服务连接成功!")
                print(f"   向量维度: {len(test_result)}")
                return embeddings
            else:
                print("⚠️ 本地BGE服务测试失败")
                
        except Exception as e:
            print(f"⚠️ 本地BGE服务连接失败: {e}")
        
        # 尝试备用服务器
        if settings.local_bge_fallback_url:
            print(f"🔄 尝试备用BGE服务: {settings.local_bge_fallback_url}")
            try:
                fallback_embeddings = RemoteBGEEmbeddings(
                    api_url=settings.local_bge_fallback_url,
                    api_key=settings.local_bge_fallback_key,
                    model_name=model_name or settings.local_bge_model_name,
                    timeout=settings.local_bge_fallback_timeout
                )
                
                test_result = fallback_embeddings.embed_query("测试连接")
                if test_result and len(test_result) > 0:
                    print("✅ 备用BGE服务连接成功!")
                    return fallback_embeddings
                else:
                    print("⚠️ 备用BGE服务也无法连接")
                    
            except Exception as e:
                print(f"⚠️ 备用BGE服务连接失败: {e}")
        
        raise ValueError("所有BGE服务连接失败，请检查配置和网络连接") 