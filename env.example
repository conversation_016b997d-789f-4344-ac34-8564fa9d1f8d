# AI Chat LangChain 环境变量配置模板
# 使用方法：复制此文件为 .env 并修改相应配置值
# Windows: copy env.template .env
# Linux/Mac: cp env.template .env

# ================================
# 应用基础配置
# ================================
APP_NAME=AI Chat LangChain
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# ================================
# 本地LLM配置 (您的Qwen3_30B_A3B模型)
# ================================
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=sk-7aAUUpWbGBOr3TYdWuJVlRhTYumzlb7Yw8nNoSIgx1WOfUg5
LOCAL_LLM_MODEL_URL=http://10.12.80.9:8077/dify/i/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B
LOCAL_LLM_TEMPERATURE=0.7
LOCAL_LLM_TIMEOUT=60

# ================================
# 嵌入模型配置 (可选 - RAG功能需要)
# ================================
LOCAL_BGE_ENABLED=false
# LOCAL_BGE_API_URL=http://your_embedding_service/v1/embeddings
# LOCAL_BGE_API_KEY=your_embedding_api_key
# LOCAL_BGE_MODEL_NAME=bge-large-zh-v1.5

# ================================
# 备用云端模型配置 (可选)
# ================================
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ================================
# LangSmith追踪配置 (可选)
# ================================
LANGCHAIN_TRACING_V2=false
# LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=ai-chat-langchain

# ================================
# 向量数据库配置 (RAG功能需要)
# ================================
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB_NAME=langchain_db

# ================================
# 其他向量存储配置 (可选)
# ================================
# PINECONE_API_KEY=your_pinecone_api_key_here
# WEAVIATE_URL=http://localhost:8080

# ================================
# 知识库查询服务配置
# ================================
KNOWLEDGE_SEARCH_API_URL=http://***********:8081/v1/workflows/run
KNOWLEDGE_SEARCH_API_KEY=app-yBG3HXjw9NiGcxXzJ4QmHEFD

# ================================
# SQL生成服务配置
# ================================
SQL_GENERATOR_API_URL=http://***********:5000/api/v0/generate_sql
SQL_GENERATOR_API_KEY=vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1

# ================================
# OpenAI API配置
# ================================
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=1000

# Azure OpenAI配置
AZURE_OPENAI_ENABLED=false
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
AZURE_OPENAI_API_VERSION=2023-12-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name_here

# MySQL数据库配置
MYSQL_HOST=***********
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database
MYSQL_CHARSET=utf8mb4

# ================================
# 省份数据库映射配置
# ================================
# 省份代码到数据库名称的映射，格式：省份代码:数据库名称:省份名称
PROVINCE_DATABASE_MAPPING=NX:analysis_nx:宁夏,GS:analysis_gs:甘肃,HE:analysis_he:河北,GZ:analysis_gz:贵州,XXL:xxltidb:特殊区域,JT:analysis_qg:总部,BJ:analysis_bj:北京,TJ:analysis_tj:天津,SX:analysis_sx:山西,NM:analysis_nm:内蒙古,LN:analysis_ln:辽宁,JL:analysis_jl:吉林,HL:analysis_hl:黑龙江,SH:analysis_sh:上海,JS:analysis_js:江苏,ZJ:analysis_zj:浙江,AH:analysis_ah:安徽,FJ:analysis_fj:福建,JX:analysis_jx:江西,SD:analysis_sd:山东,HA:analysis_ha:河南,HB:analysis_hb:湖北,HN:analysis_hn:湖南,GD:analysis_gd:广东,GX:analysis_gx:广西,HI:analysis_hi:海南,CQ:analysis_cq:重庆,SC:analysis_sc:四川,YN:analysis_yn:云南,XZ:analysis_xz:西藏,SN:analysis_sn:陕西,QH:analysis_qh:青海,XJ:analysis_xj:新疆

# ================================
# 远程Embedding模型配置
# ================================
# 是否启用远程embedding服务
REMOTE_EMBEDDING_ENABLED=false
# 远程embedding服务URL
REMOTE_EMBEDDING_URL=http://localhost:8001/embeddings
# 远程embedding服务API密钥
REMOTE_EMBEDDING_API_KEY=your-api-key-here
# 远程embedding模型名称
REMOTE_EMBEDDING_MODEL=text-embedding-ada-002

# PostgreSQL数据库配置（可选）
POSTGRESQL_HOST=localhost
POSTGRESQL_PORT=5432
POSTGRESQL_USER=postgres
POSTGRESQL_PASSWORD=your_postgres_password
POSTGRESQL_DATABASE=your_postgres_database 