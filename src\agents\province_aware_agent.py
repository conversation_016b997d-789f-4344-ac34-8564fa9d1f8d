"""
省份感知的Agent
能够从用户消息中提取省份信息并传递给工具
"""

import re
import json
import logging
from typing import Dict, Any, Optional, List
from src.agents.enhanced_tool_calling_agent import EnhancedToolCallingAgent
from src.config import settings

logger = logging.getLogger(__name__)


class ProvinceAwareAgent(EnhancedToolCallingAgent):
    """
    省份感知的Agent，能够自动提取省份代码并传递给integrated_sql工具
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.current_system_message = None
    
    def process_message(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                       system_message: str = None) -> Dict[str, Any]:
        """
        处理用户消息，支持传递system消息
        
        Args:
            message: 用户消息
            conversation_history: 对话历史
            system_message: 系统消息，用于提取省份代码
            
        Returns:
            包含回复和工具调用结果的字典
        """
        # 保存当前的system消息，供工具调用时使用
        self.current_system_message = system_message
        
        if system_message:
            logger.info(f"🌍 [省份Agent] 接收到系统消息: {system_message}")
            
            # 提取省份代码
            province_code = self._extract_province_code(system_message)
            if province_code:
                logger.info(f"🎯 [省份Agent] 提取到省份代码: {province_code}")
                
                # 验证省份代码是否有效
                database_name = settings.get_database_name_by_province(province_code)
                if database_name:
                    logger.info(f"✅ [省份Agent] 省份代码 {province_code} 对应数据库: {database_name}")
                else:
                    logger.warning(f"⚠️ [省份Agent] 省份代码 {province_code} 未找到对应数据库配置")
        
        # 调用父类的处理方法，传递system_message
        return super().process_message(message, conversation_history, system_message)
    
    async def process_message_async(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                                  real_time_callback: Optional[callable] = None,
                                  system_message: str = None) -> Dict[str, Any]:
        """
        异步处理用户消息，支持传递system消息
        
        Args:
            message: 用户消息
            conversation_history: 对话历史
            real_time_callback: 实时回调函数
            system_message: 系统消息，用于提取省份代码
            
        Returns:
            包含回复和工具调用结果的字典
        """
        # 保存当前的system消息，供工具调用时使用
        self.current_system_message = system_message
        
        if system_message:
            logger.info(f"🌍 [省份Agent] 接收到系统消息: {system_message}")
            
            # 提取省份代码
            province_code = self._extract_province_code(system_message)
            if province_code:
                logger.info(f"🎯 [省份Agent] 提取到省份代码: {province_code}")
                
                # 验证省份代码是否有效
                database_name = settings.get_database_name_by_province(province_code)
                if database_name:
                    logger.info(f"✅ [省份Agent] 省份代码 {province_code} 对应数据库: {database_name}")
                else:
                    logger.warning(f"⚠️ [省份Agent] 省份代码 {province_code} 未找到对应数据库配置")
        
        # 调用父类的异步处理方法
        return await super().process_message_async(message, conversation_history, real_time_callback)
    
    def _extract_province_code(self, system_message: str) -> Optional[str]:
        """
        从系统消息中提取省份代码
        
        Args:
            system_message: 系统消息内容
            
        Returns:
            str: 省份代码，如 'GZ'，如果未找到返回None
        """
        if not system_message:
            return None
        
        # 查找"数据库:省份代码"的模式
        pattern = r'数据库\s*[:：]\s*([A-Z]{1,3})'
        match = re.search(pattern, system_message)
        if match:
            province_code = match.group(1).upper()
            return province_code
        
        # 备用模式：直接查找大写字母组合
        pattern2 = r'\b([A-Z]{2,3})\b'
        matches = re.findall(pattern2, system_message)
        if matches:
            # 检查是否是有效的省份代码
            province_mapping = settings.get_province_database_mapping()
            for match in matches:
                if match in province_mapping:
                    return match
        
        return None
    
    def _execute_tool(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写工具执行方法，为integrated_sql工具自动添加system_message参数
        """
        tool_name = tool_call.get("tool_name")
        parameters = tool_call.get("parameters", {})
        
        # 如果是integrated_sql工具且有system消息，自动添加system_message参数
        if tool_name == "integrated_sql" and self.current_system_message:
            if "system_message" not in parameters:
                parameters["system_message"] = self.current_system_message
                logger.info(f"🔧 [省份Agent] 为integrated_sql工具自动添加system_message参数")
        
        # 更新tool_call中的参数
        tool_call["parameters"] = parameters
        
        # 调用父类的工具执行方法
        return super()._execute_tool(tool_call)
