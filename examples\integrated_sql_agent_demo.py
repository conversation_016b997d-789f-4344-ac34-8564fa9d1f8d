"""
集成SQL工具Agent演示
对比分离SQL工具和集成SQL工具的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core import LLMFactory
from src.agents import EnhancedToolCallingAgent, AgentExecutor
from src.tools import get_all_tools, get_basic_tools, get_sql_tools
from src.config import settings


def test_integrated_sql_agent():
    """测试集成SQL工具的Agent"""
    print("🔬 测试集成SQL工具Agent")
    print("=" * 50)
    
    # 检查配置
    if not settings.local_llm_enabled:
        print("❌ 请先配置千问模型")
        return None
    
    # 创建LLM
    llm = LLMFactory.create_chat_model(provider="local", temperature=0.1)
    
    # 创建使用集成SQL工具的Agent
    integrated_tools = get_basic_tools() + get_sql_tools(integrated=True)
    agent = EnhancedToolCallingAgent(llm=llm, tools=integrated_tools, max_iterations=3)
    executor = AgentExecutor(agent)
    
    print(f"✅ 集成SQL Agent初始化完成")
    print(f"📋 工具列表: {[tool.name for tool in agent.tools]}")
    
    return executor


def test_separated_sql_agent():
    """测试分离SQL工具的Agent"""
    print("🔬 测试分离SQL工具Agent")
    print("=" * 50)
    
    # 检查配置
    if not settings.local_llm_enabled:
        print("❌ 请先配置千问模型")
        return None
    
    # 创建LLM
    llm = LLMFactory.create_chat_model(provider="local", temperature=0.1)
    
    # 创建使用分离SQL工具的Agent
    separated_tools = get_basic_tools() + get_sql_tools(integrated=False)
    agent = EnhancedToolCallingAgent(llm=llm, tools=separated_tools, max_iterations=5)
    executor = AgentExecutor(agent)
    
    print(f"✅ 分离SQL Agent初始化完成")
    print(f"📋 工具列表: {[tool.name for tool in agent.tools]}")
    
    return executor


def compare_agents():
    """对比两种Agent的表现"""
    print("🆚 对比集成SQL工具 vs 分离SQL工具")
    print("=" * 60)
    
    # 创建两个Agent
    integrated_executor = test_integrated_sql_agent()
    if not integrated_executor:
        return
    
    print("\n" + "-" * 60 + "\n")
    
    separated_executor = test_separated_sql_agent()
    if not separated_executor:
        return
    
    # 测试问题
    test_questions = [
        "2025年4月的电费是多少？",
        "查询用户张三的所有电费记录",
        "统计每个月的总电费"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{'='*80}")
        print(f"📝 测试问题 {i}: {question}")
        print(f"{'='*80}")
        
        # 测试集成版本
        print(f"\n🔧 集成SQL工具测试:")
        print("-" * 40)
        integrated_result = integrated_executor.chat(question)
        print(f"🤖 回复: {integrated_result['response']}")
        
        if integrated_result.get("tool_used") and "tool_calls_history" in integrated_result:
            tools_used = [info["tool_call"]["tool_name"] for info in integrated_result["tool_calls_history"]]
            print(f"🔧 工具调用: {' → '.join(tools_used)} ({len(tools_used)}步)")
            print(f"⏱️ 迭代次数: {integrated_result.get('iterations', 0)}")
        
        # 测试分离版本
        print(f"\n🔨 分离SQL工具测试:")
        print("-" * 40)
        separated_result = separated_executor.chat(question)
        print(f"🤖 回复: {separated_result['response']}")
        
        if separated_result.get("tool_used") and "tool_calls_history" in separated_result:
            tools_used = [info["tool_call"]["tool_name"] for info in separated_result["tool_calls_history"]]
            print(f"🔧 工具调用: {' → '.join(tools_used)} ({len(tools_used)}步)")
            print(f"⏱️ 迭代次数: {separated_result.get('iterations', 0)}")
        
        # 对比总结
        print(f"\n📊 对比总结:")
        integrated_steps = len(integrated_result.get("tool_calls_history", []))
        separated_steps = len(separated_result.get("tool_calls_history", []))
        
        print(f"   集成版本: {integrated_steps} 步工具调用")
        print(f"   分离版本: {separated_steps} 步工具调用")
        
        if integrated_steps < separated_steps:
            print("   ✅ 集成版本更高效")
        elif integrated_steps == separated_steps:
            print("   ⚖️ 两个版本效率相当")
        else:
            print("   ❓ 分离版本步骤更少（可能未完成完整流程）")
    
    # 显示最终统计
    print(f"\n📈 最终统计对比:")
    print("-" * 40)
    
    integrated_stats = integrated_executor.get_stats()
    separated_stats = separated_executor.get_stats()
    
    print(f"集成版本:")
    print(f"  总工具调用: {integrated_stats['tool_calls']}")
    print(f"  成功率: {integrated_stats['tool_success_rate']:.1%}")
    
    print(f"分离版本:")
    print(f"  总工具调用: {separated_stats['tool_calls']}")
    print(f"  成功率: {separated_stats['tool_success_rate']:.1%}")


def interactive_test():
    """交互式测试"""
    print("\n🎯 交互式测试模式")
    print("选择要测试的工具类型:")
    print("1. 集成SQL工具 (推荐)")
    print("2. 分离SQL工具")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            executor = test_integrated_sql_agent()
            if executor:
                interactive_chat(executor, "集成SQL工具")
            break
        elif choice == "2":
            executor = test_separated_sql_agent()
            if executor:
                interactive_chat(executor, "分离SQL工具")
            break
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("请输入有效选择 (1/2/3)")


def interactive_chat(executor: AgentExecutor, agent_type: str):
    """交互式聊天"""
    print(f"\n💬 {agent_type} 交互模式")
    print("输入 'quit' 退出，'stats' 查看统计")
    print("-" * 40)
    
    while True:
        try:
            user_input = input(f"\n👤 用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 退出聊天")
                break
            
            if user_input.lower() in ['stats', '统计']:
                stats = executor.get_stats()
                tool_usage = executor.get_tool_usage_summary()
                print(f"\n📊 统计信息:")
                print(f"   消息数: {stats['total_messages']}")
                print(f"   工具调用: {stats['tool_calls']}")
                print(f"   成功率: {stats['tool_success_rate']:.1%}")
                if tool_usage:
                    print(f"   工具详情:")
                    for tool_name, usage in tool_usage.items():
                        print(f"     {tool_name}: {usage['total_calls']}次调用")
                continue
            
            if not user_input:
                continue
            
            # 处理用户消息
            result = executor.chat(user_input)
            print(f"\n🤖 Agent: {result['response']}")
            
            # 显示工具调用信息
            if result.get("tool_used") and "tool_calls_history" in result:
                tools_used = [info["tool_call"]["tool_name"] for info in result["tool_calls_history"]]
                print(f"🔧 使用工具: {' → '.join(tools_used)} ({len(tools_used)}步)")
        
        except KeyboardInterrupt:
            print("\n👋 程序中断")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")


def main():
    """主函数"""
    print("🚀 集成SQL工具演示程序")
    print("=" * 50)
    
    print("本程序将演示:")
    print("1. 集成SQL工具 - 一步完成SQL生成和执行")
    print("2. 分离SQL工具 - 需要多步调用")
    print("3. 两种方式的效果对比")
    
    while True:
        print(f"\n选择运行模式:")
        print("1. 自动对比测试")
        print("2. 交互式测试")
        print("3. 退出")
        
        choice = input("\n请选择 (1/2/3): ").strip()
        
        if choice == "1":
            compare_agents()
            break
        elif choice == "2":
            interactive_test()
            break
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("请输入有效选择 (1/2/3)")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 程序出错: {e}")
        import traceback
        traceback.print_exc() 