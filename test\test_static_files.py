#!/usr/bin/env python3
"""
测试静态文件访问
"""

import requests
import sys

def test_static_files():
    """测试静态文件访问"""
    print("🧪 测试静态文件访问")
    print("=" * 50)
    
    base_url = "http://localhost:8001"
    
    # 测试文件列表
    test_files = [
        "/admin",  # 管理页面
        "/static/js/prompt_management.js",  # JavaScript文件
        "/static/prompt_management.html",  # HTML文件（直接访问）
        "/docs",  # API文档
    ]
    
    for file_path in test_files:
        url = base_url + file_path
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {file_path} - 访问成功")
                if file_path.endswith('.js'):
                    print(f"   JavaScript文件大小: {len(response.content)} 字节")
                elif file_path.endswith('.html') or file_path == '/admin':
                    print(f"   HTML内容长度: {len(response.content)} 字节")
            else:
                print(f"❌ {file_path} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {file_path} - 错误: {e}")
    
    print("\n" + "=" * 50)
    print("💡 如果JavaScript文件访问失败，请检查:")
    print("1. 服务器是否正在运行")
    print("2. 静态文件路径是否正确")
    print("3. 文件权限是否正确")

if __name__ == "__main__":
    test_static_files()
