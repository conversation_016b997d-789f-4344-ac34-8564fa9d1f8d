"""
基础工具类定义
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class ToolInput(BaseModel):
    """工具输入的基类"""
    pass


class ToolOutput(BaseModel):
    """工具输出的基类"""
    success: bool = Field(description="操作是否成功")
    result: Any = Field(description="操作结果")
    error: Optional[str] = Field(default=None, description="错误信息")


class BaseTool(ABC):
    """基础工具类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    @abstractmethod
    def call(self, *args, **kwargs) -> ToolOutput:
        """执行工具功能"""
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """获取工具的输入输出模式"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "schema": self.get_schema()
        }


def get_all_tools() -> List[BaseTool]:
    """获取所有可用工具"""
    from .integrated_sql_tools import IntegratedSQLTool
    
    return [
        IntegratedSQLTool()
    ] 