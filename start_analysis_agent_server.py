#!/usr/bin/env python3
"""
启动 Analysis Agent API 服务器
专门提供AI驱动的自然语言到SQL查询服务，兼容Dify等平台接入
支持通过环境变量配置服务器地址和端口
"""

import sys
import os
import subprocess
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置
from src.config.settings import settings

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Analysis Agent 服务器启动脚本")
    parser.add_argument(
        "--host",
        default=None,
        help=f"服务器主机地址 (默认从环境变量读取: {settings.server_host})"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help=f"服务器端口 (默认从环境变量读取: {settings.server_port})"
    )
    parser.add_argument(
        "--show-config",
        action="store_true",
        help="显示当前配置信息"
    )

    args = parser.parse_args()

    # 确定最终使用的主机和端口
    final_host = args.host if args.host is not None else settings.server_host
    final_port = args.port if args.port is not None else settings.server_port

    # 显示配置信息
    if args.show_config:
        print("🔧 Analysis Agent 服务器配置")
        print("=" * 50)
        print(f"📡 服务器地址: {final_host}")
        if args.host is not None:
            print(f"   (通过命令行参数设置，环境变量默认: {settings.server_host})")
        else:
            print(f"   (从环境变量读取)")
        print(f"🔌 服务器端口: {final_port}")
        if args.port is not None:
            print(f"   (通过命令行参数设置，环境变量默认: {settings.server_port})")
        else:
            print(f"   (从环境变量读取)")
        print(f"🔄 最大迭代次数: {settings.agent_max_iterations}")
        print(f"⏱️ 超时时间: {settings.agent_timeout}秒")
        print(f"🌡️ LLM超时: {settings.local_llm_timeout}秒")
        print("=" * 50)
        print("💡 提示: 可以通过修改 .env 文件来调整这些配置")
        return


    print("🤖 启动 Analysis Agent API 服务器")
    print("=" * 50)
    print("📋 功能：自然语言 → 数据分析 → 结果返回")
    print("🎯 适用：Dify、其他AI平台、API调用")
    print(f"📡 主机地址: {final_host}")
    print(f"🔌 端口: {final_port}")
    print(f"🔄 最大迭代: {settings.agent_max_iterations}次")
    print(f"⏱️ 超时时间: {settings.agent_timeout}秒")
    print("=" * 50)
    
    # 检查环境变量文件
    env_file = ".env"
    if not os.path.exists(env_file):
        print("❌ 未找到 .env 配置文件")
        print("\n请创建 .env 文件，包含以下配置：")
        print("""
# ===== LLM配置 =====
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_api_key_here
LOCAL_LLM_MODEL_URL=http://localhost:9997/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B

# ===== API服务器配置 =====
API_SERVER_HOST=0.0.0.0
API_SERVER_PORT=8001

# ===== MySQL数据库配置 =====
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database

# ===== SQL生成API配置 =====
SQL_GENERATOR_API_URL=http://***********:5000/api/v0/generate_sql
SQL_GENERATOR_API_KEY=vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1
        """)
        return
    
    print("✅ 找到配置文件，正在启动服务器...\n")
    
    # 检查必要的依赖
    try:
        import fastapi
        import uvicorn
        import pymysql
        print("✅ 检查依赖包：FastAPI, Uvicorn, PyMySQL 已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请运行: pip install fastapi uvicorn pymysql")
        return
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = os.getcwd()
    
    try:
        print("🚀 正在启动Analysis Agent服务器...")
        print("⏳ 初始化中，请稍候...\n")
        
        # 启动服务器
        cmd = [
            sys.executable,
            "examples/analysis_agent_server.py",
            "--mode", "server",
            "--host", final_host,
            "--port", str(final_port)
        ]

        print(f"🔧 执行命令: {' '.join(cmd)}")
        print()

        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        print("\n可能的解决方案：")
        print("1. 检查.env配置是否正确")
        print("2. 确保LLM服务正在运行")
        print("3. 检查数据库连接配置")
        print("4. 查看详细错误日志")
    except FileNotFoundError:
        print("❌ 找不到服务器文件，请确保文件路径正确")


def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 50)
    print("📖 使用示例")
    print("=" * 50)
    
    print("\n1. 直接API调用:")
    print("   POST http://localhost:8001/v1/sql/query")
    print("   {\"question\": \"2025年4月电费是多少\"}")
    
    print("\n2. Dify平台接入:")
    print("   模型提供商: 自定义")
    print("   API Base URL: http://localhost:8001/v1")
    print("   API Key: 任意字符串")
    print("   模型名称: sql-agent")
    
    print("\n3. OpenAI兼容接口:")
    print("   POST http://localhost:8001/v1/chat/completions")
    print("   标准OpenAI格式，可用于任何支持OpenAI API的平台")
    
    print("\n4. 健康检查:")
    print("   GET http://localhost:8001/health")
    
    print("\n5. API文档:")
    print("   http://localhost:8001/docs")
    
    print("\n" + "=" * 50)


if __name__ == "__main__":
    try:
        main()
        # 如果正常结束，显示使用示例
        show_usage_examples()
    except KeyboardInterrupt:
        print("\n👋 用户中断启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n📖 查看使用示例和故障排除：")
        show_usage_examples() 