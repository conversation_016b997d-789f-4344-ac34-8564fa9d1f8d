#!/usr/bin/env python3
"""
测试数据库切换功能
"""

import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.vector_enhanced_agent import VectorEnhancedAgent

def test_database_switch_functionality():
    """测试数据库切换功能"""
    print("🧪 测试数据库切换功能")
    print("=" * 60)
    
    try:
        # 创建VectorEnhancedAgent实例
        agent = VectorEnhancedAgent()
        
        # 模拟用户请求
        test_cases = [
            {
                "system": "数据库:GZ",
                "message": "本省电费与全国对比如何",
                "description": "测试省份对比分析（需要切换到JT公共库）"
            },
            {
                "system": "数据库:BJ", 
                "message": "多省份数据汇总分析",
                "description": "测试多省份数据汇总（需要切换多个数据库）"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 测试用例 {i}: {test_case['description']}")
            print(f"📝 System: {test_case['system']}")
            print(f"📝 Message: {test_case['message']}")
            
            # 调用向量增强处理
            result = agent.process_message(
                message=test_case['message'],
                system_message=test_case['system']
            )
            
            print(f"📊 处理结果:")
            print(f"  使用复杂提示词: {result.get('used_complex_prompt', False)}")
            
            if result.get('used_complex_prompt'):
                complex_prompt_info = result.get('complex_prompt_info', {})
                print(f"  匹配的提示词: {complex_prompt_info.get('title', 'Unknown')}")
                print(f"  相似度: {complex_prompt_info.get('similarity', 0):.1%}")
                
                # 显示增强的系统提示词（如果有的话）
                enhanced_system = result.get('enhanced_system_prompt')
                if enhanced_system:
                    print(f"  增强系统提示词长度: {len(enhanced_system)} 字符")
                    
                    # 检查是否包含数据库切换指令
                    if "[内部指令]" in enhanced_system:
                        print("  ✅ 包含数据库切换指令")
                    else:
                        print("  ❌ 未检测到数据库切换指令")
            else:
                print("  ❌ 未匹配到复杂提示词")
        
        print("\n" + "=" * 60)
        print("✅ 数据库切换功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_switch_parsing():
    """测试数据库切换命令解析"""
    print("\n🧪 测试数据库切换命令解析")
    print("=" * 60)
    
    try:
        agent = VectorEnhancedAgent()
        agent.original_province_code = "GZ"
        
        # 测试各种数据库切换命令
        test_steps = [
            "查询本省基础数据",
            "[切换数据库:JT] 查询全国汇总数据",
            "[切换数据库：BJ] 查询北京数据（测试中文冒号）",
            "分析数据差异",
            "[切换数据库:原始] 切换回原始数据库",
            "生成综合报告"
        ]
        
        print("📝 测试处理步骤:")
        for i, step in enumerate(test_steps, 1):
            print(f"  {i}. {step}")
        
        # 解析数据库切换命令
        enhanced_steps = agent._parse_database_switch_commands(test_steps)
        
        print("\n🔄 解析后的步骤:")
        for i, step in enumerate(enhanced_steps, 1):
            if step.startswith("[内部指令]"):
                print(f"  {i}. 🔄 {step}")
            else:
                print(f"  {i}. {step}")
        
        # 测试system消息修改
        print("\n🧪 测试system消息修改:")
        original_system = "数据库:GZ 其他配置"
        
        test_targets = ["JT", "BJ", "SH"]
        for target in test_targets:
            modified = agent._modify_system_message_for_database_switch(original_system, target)
            print(f"  {target}: {original_system} → {modified}")
        
        print("\n✅ 数据库切换命令解析测试完成")
        
    except Exception as e:
        print(f"❌ 解析测试失败: {e}")
        import traceback
        traceback.print_exc()

def show_usage_examples():
    """显示使用示例"""
    print("\n📋 数据库切换功能使用指南")
    print("=" * 60)
    
    print("🎯 支持的数据库切换语法:")
    print("  [切换数据库:JT] - 切换到公共库")
    print("  [切换数据库:GZ] - 切换到贵州库")
    print("  [切换数据库:BJ] - 切换到北京库")
    print("  [切换数据库:原始] - 切换回原始数据库")
    
    print("\n📝 示例处理步骤:")
    examples = [
        "1. 查询本省电费数据",
        "2. [切换数据库:JT] 查询全国平均电费",
        "3. [切换数据库:原始] 对比分析本省与全国差异",
        "4. 生成对比分析报告"
    ]
    
    for example in examples:
        print(f"  {example}")
    
    print("\n🔧 工作原理:")
    print("  1. 系统解析处理步骤中的切换命令")
    print("  2. 自动修改后续SQL查询的数据库配置")
    print("  3. 在不同数据库间无缝切换")
    print("  4. 汇总多数据源的查询结果")
    
    print("\n💡 使用场景:")
    print("  - 省份对比分析（本省 vs 全国）")
    print("  - 多省份数据汇总")
    print("  - 跨区域数据对比")
    print("  - 全国排名分析")

if __name__ == "__main__":
    print("🚀 数据库切换功能测试")
    print("=" * 60)
    
    # 测试解析功能
    test_database_switch_parsing()
    
    # 测试完整功能（需要先创建示例数据）
    print("\n💡 要测试完整功能，请先运行:")
    print("  python examples/database_switch_example.py")
    print("  python examples/analysis_agent_server.py")
    
    # 显示使用指南
    show_usage_examples()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
