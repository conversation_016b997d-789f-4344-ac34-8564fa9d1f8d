#!/usr/bin/env python3
"""测试明确传入show_think_tags参数"""

import requests
import json

def test_think_tags():
    """测试think标签显示"""
    # 测试本地和服务器两个地址
    urls = [
        ("本地", "http://127.0.0.1:8001/v1/chat/completions"),
        ("服务器", "http://10.12.22.20:8001/v1/chat/completions")
    ]
    
    for url_name, url in urls:
        print(f"\n{'='*20} 测试 {url_name} {'='*20}")
        print(f"地址: {url}")
        
        # 测试1：明确传入show_think_tags=true
        print(f"\n=== {url_name} - show_think_tags=true ===")
        data1 = {
            "model": "analysis-agent",
            "messages": [
                {"role": "user", "content": "2025年4月电费是多少？"}
            ],
            "stream": False,
            "show_think_tags": True
        }
        
        try:
            response1 = requests.post(url, json=data1, timeout=30)
            print(f"状态码: {response1.status_code}")
            if response1.status_code == 200:
                result1 = response1.json()
                content1 = result1["choices"][0]["message"]["content"]
                print("回复内容:")
                print(content1[:500] + "..." if len(content1) > 500 else content1)
                print(f"\n包含<think>标签: {'<think>' in content1}")
                print(f"包含</think>标签: {'</think>' in content1}")
            else:
                print(f"错误: {response1.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        print("\n" + "-"*50)
        
        # 测试2：不传入show_think_tags参数（测试默认值）
        print(f"\n=== {url_name} - 不传入show_think_tags参数（测试默认值） ===")
        data2 = {
            "model": "analysis-agent",
            "messages": [
                {"role": "user", "content": "2025年4月电费是多少？"}
            ],
            "stream": False
        }
        
        try:
            response2 = requests.post(url, json=data2, timeout=30)
            print(f"状态码: {response2.status_code}")
            if response2.status_code == 200:
                result2 = response2.json()
                content2 = result2["choices"][0]["message"]["content"]
                print("回复内容:")
                print(content2[:500] + "..." if len(content2) > 500 else content2)
                print(f"\n包含<think>标签: {'<think>' in content2}")
                print(f"包含</think>标签: {'</think>' in content2}")
            else:
                print(f"错误: {response2.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        print("\n" + "="*60)

if __name__ == "__main__":
    test_think_tags() 