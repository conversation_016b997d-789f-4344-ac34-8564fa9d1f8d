#!/usr/bin/env python3
"""服务器诊断脚本 - 检查服务器代码版本和功能"""

import requests
import json

def test_server_debug():
    """诊断服务器问题"""
    server_url = "http://10.12.22.20:8001"
    
    print("🔍 服务器诊断开始")
    print("="*60)
    
    # 1. 检查服务器是否在线
    print("\n1. 检查服务器状态...")
    try:
        response = requests.get(f"{server_url}/", timeout=10)
        print(f"✅ 服务器在线，状态码: {response.status_code}")
        if response.status_code == 200:
            print("服务器根路径返回:")
            print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return
    
    # 2. 检查健康状态
    print("\n2. 检查健康状态...")
    try:
        response = requests.get(f"{server_url}/health", timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("健康检查返回:")
            print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"健康检查失败: {e}")
    
    # 3. 检查模型列表
    print("\n3. 检查可用模型...")
    try:
        response = requests.get(f"{server_url}/v1/models", timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("模型列表:")
            print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"模型列表获取失败: {e}")
    
    # 4. 测试不同的请求格式
    print("\n4. 测试不同请求格式...")
    
    test_cases = [
        {
            "name": "标准请求（无show_think_tags）",
            "data": {
                "model": "analysis-agent",
                "messages": [{"role": "user", "content": "你好"}],
                "stream": False
            }
        },
        {
            "name": "明确show_think_tags=true",
            "data": {
                "model": "analysis-agent", 
                "messages": [{"role": "user", "content": "你好"}],
                "stream": False,
                "show_think_tags": True
            }
        },
        {
            "name": "明确show_think_tags=false",
            "data": {
                "model": "analysis-agent",
                "messages": [{"role": "user", "content": "你好"}],
                "stream": False,
                "show_think_tags": False
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        try:
            response = requests.post(
                f"{server_url}/v1/chat/completions",
                json=test_case['data'],
                timeout=30
            )
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"回复长度: {len(content)} 字符")
                print(f"包含<think>: {'<think>' in content}")
                print(f"包含</think>: {'</think>' in content}")
                print("回复内容预览:")
                print(content[:200] + "..." if len(content) > 200 else content)
            else:
                print(f"请求失败: {response.text[:200]}")
                
        except Exception as e:
            print(f"测试失败: {e}")
    
    # 5. 检查请求参数是否被正确接收
    print("\n5. 详细参数检查...")
    test_data = {
        "model": "analysis-agent",
        "messages": [{"role": "user", "content": "测试show_think_tags参数"}],
        "stream": False,
        "show_think_tags": True,
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    print("发送的完整请求数据:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            f"{server_url}/v1/chat/completions",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print("响应头信息:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
            
        if response.status_code == 200:
            result = response.json()
            print("\n完整响应结构:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"\n错误响应: {response.text}")
            
    except Exception as e:
        print(f"详细测试失败: {e}")
    
    print("\n" + "="*60)
    print("🔍 诊断完成")

if __name__ == "__main__":
    test_server_debug() 