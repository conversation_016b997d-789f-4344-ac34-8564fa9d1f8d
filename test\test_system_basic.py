#!/usr/bin/env python3
"""
基础系统测试 - 不依赖embedding模型
"""

import sys
import os
import json
import requests
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_api_server():
    """测试API服务器基础功能"""
    print("🧪 测试API服务器基础功能")
    print("=" * 50)
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务器运行正常")
        else:
            print(f"⚠️ 健康检查返回: {response.status_code}")
    except:
        print("❌ 无法连接到API服务器")
        print("💡 请先启动服务器: python examples/analysis_agent_server.py")
        return False
    
    # 测试管理界面
    try:
        response = requests.get("http://localhost:8001/admin", timeout=5)
        if response.status_code == 200:
            print("✅ 管理界面可访问")
            print("🌐 管理界面地址: http://localhost:8001/admin")
        else:
            print(f"❌ 管理界面访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 管理界面测试失败: {e}")
    
    # 测试API文档
    try:
        response = requests.get("http://localhost:8001/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档可访问")
            print("📚 API文档地址: http://localhost:8001/docs")
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API文档测试失败: {e}")
    
    return True

def test_prompt_api_basic():
    """测试复杂提示词API基础功能"""
    print("\n🧪 测试复杂提示词API")
    print("=" * 50)
    
    base_url = "http://localhost:8001/api/prompts"
    
    # 测试获取提示词列表
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            prompts = response.json()
            print(f"✅ 获取提示词列表成功，共 {len(prompts)} 个")
        else:
            print(f"❌ 获取提示词列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API请求失败: {e}")
    
    # 测试统计信息
    try:
        response = requests.get(f"{base_url}/stats/summary", timeout=10)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 统计信息获取成功")
            print(f"   总提示词数: {stats.get('total_prompts', 0)}")
        else:
            print(f"❌ 统计信息获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 统计信息请求失败: {e}")

def test_chat_api_basic():
    """测试聊天API基础功能"""
    print("\n🧪 测试聊天API基础功能")
    print("=" * 50)
    
    api_url = "http://localhost:8001/v1/chat/completions"
    
    # 简单测试用例
    test_cases = [
        {
            "name": "基础查询测试",
            "message": "你好",
            "system": "数据库:GZ"
        },
        {
            "name": "省份感知测试",
            "message": "查询数据",
            "system": "数据库:BJ"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        
        request_body = {
            "model": "analysis_agent",
            "messages": [
                {"role": "system", "content": test_case['system']},
                {"role": "user", "content": test_case['message']}
            ],
            "stream": False,
            "show_think_tags": False,
            "max_tokens": 100  # 限制输出长度
        }
        
        try:
            start_time = time.time()
            response = requests.post(api_url, json=request_body, timeout=30)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 响应成功 (用时: {duration:.2f}秒)")
                
                # 检查响应格式
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"📄 响应长度: {len(content)} 字符")
                    
                    # 检查是否有向量增强信息
                    if 'used_complex_prompt' in result:
                        used_complex = result['used_complex_prompt']
                        print(f"🎯 使用复杂提示词: {'是' if used_complex else '否'}")
                    
                else:
                    print("⚠️ 响应格式异常")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                if response.text:
                    print(f"   错误信息: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(1)

def test_province_mapping():
    """测试省份映射功能"""
    print("\n🧪 测试省份映射功能")
    print("=" * 50)
    
    try:
        from src.config import settings
        
        # 测试省份映射解析
        mapping = settings.get_province_database_mapping()
        print(f"✅ 省份映射解析成功，共 {len(mapping)} 个省份")
        
        # 测试几个省份代码
        test_codes = ['GZ', 'BJ', 'SH', 'XXL']
        for code in test_codes:
            db_name = settings.get_database_name_by_province(code)
            if db_name:
                print(f"   {code} -> {db_name}")
            else:
                print(f"   {code} -> 未找到映射")
                
    except Exception as e:
        print(f"❌ 省份映射测试失败: {e}")

def test_file_structure():
    """测试文件结构"""
    print("\n🧪 测试文件结构")
    print("=" * 50)
    
    required_files = [
        "src/agents/vector_enhanced_agent.py",
        "src/core/prompt_vectorstore_manager.py",
        "src/api/prompt_management_api.py",
        "src/web/static/prompt_management.html",
        "src/web/static/js/prompt_management.js"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")

def main():
    """主函数"""
    print("🚀 向量增强系统基础测试")
    print("=" * 60)
    print("📝 注意: 此测试不依赖embedding模型，仅测试基础功能")
    print()
    
    # 测试文件结构
    test_file_structure()
    
    # 测试省份映射
    test_province_mapping()
    
    # 测试API服务器
    if not test_api_server():
        return
    
    # 测试API功能
    test_prompt_api_basic()
    test_chat_api_basic()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("✅ 基础功能测试完成")
    print()
    print("🔧 下一步配置:")
    print("1. 配置您的远程embedding服务:")
    print("   在.env中设置 REMOTE_EMBEDDING_ENABLED=true")
    print("   设置 REMOTE_EMBEDDING_URL=您的embedding服务地址")
    print()
    print("2. 创建示例复杂提示词:")
    print("   python examples/sample_complex_prompts.py")
    print()
    print("3. 访问管理界面:")
    print("   http://localhost:8001/admin")
    print()
    print("4. 测试完整功能:")
    print("   python test_vector_enhanced_system.py")

if __name__ == "__main__":
    main()
