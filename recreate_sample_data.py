#!/usr/bin/env python3
"""
重新创建示例数据
清理旧数据并使用新的向量化逻辑创建示例提示词
"""

import sys
import os
import shutil
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def clear_vector_database():
    """清理向量数据库"""
    print("🧹 清理向量数据库...")
    
    db_path = "./data/prompt_chroma_db"
    if os.path.exists(db_path):
        try:
            shutil.rmtree(db_path)
            print(f"✅ 已删除旧数据库: {db_path}")
        except Exception as e:
            print(f"❌ 删除失败: {e}")
    else:
        print("📁 数据库目录不存在，无需清理")

def recreate_sample_data():
    """重新创建示例数据"""
    print("\n🔄 重新创建示例数据...")
    
    try:
        # 导入并运行示例数据创建
        from examples.sample_complex_prompts import create_sample_prompts
        
        success = create_sample_prompts()
        
        if success:
            print("✅ 示例数据创建成功！")
            return True
        else:
            print("❌ 示例数据创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_new_search():
    """测试新的搜索功能"""
    print("\n🧪 测试新的搜索功能...")
    
    try:
        from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager
        
        manager = get_prompt_vectorstore_manager()
        
        # 测试搜索
        test_queries = [
            "电费情况",
            "电费分析", 
            "当前电费情况如何",
            "铁塔服务费",
            "2025年4月铁塔服务费是多少",
            "指标对比",
            "异常数据"
        ]
        
        print("🔍 搜索测试结果:")
        for query in test_queries:
            results = manager.search_complex_prompts(query, k=2, score_threshold=0.3)
            print(f"\n'{query}':")
            if results:
                for prompt, similarity in results:
                    print(f"  ✅ {prompt.title}: {similarity:.1%}")
            else:
                print(f"  ❌ 无匹配结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 重新创建示例数据")
    print("=" * 60)
    
    # 1. 清理旧数据
    clear_vector_database()
    
    # 2. 重新创建数据
    if recreate_sample_data():
        # 3. 测试搜索
        if test_new_search():
            print("\n" + "=" * 60)
            print("🎉 所有操作完成！")
            print("\n💡 现在您可以:")
            print("1. 启动API服务器: python examples/analysis_agent_server.py")
            print("2. 访问管理界面: http://localhost:8001/admin")
            print("3. 在搜索测试中尝试以下问题:")
            print("   - '电费情况'")
            print("   - '当前电费情况如何'") 
            print("   - '铁塔服务费'")
            print("   - '2025年4月铁塔服务费是多少'")
            print("   - '指标对比'")
            print("   - '异常数据'")
        else:
            print("\n❌ 搜索测试失败")
    else:
        print("\n❌ 数据创建失败")

if __name__ == "__main__":
    main()
