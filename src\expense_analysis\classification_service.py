#!/usr/bin/env python3
"""
费用分类服务
统一的分类API服务，支持Token鉴权和多数据库切换
"""
import os
import sys
import argparse
import time
import threading

import concurrent.futures
import json
from datetime import datetime
from typing import List, Dict, Optional, Any
from pathlib import Path
from fastapi import FastAPI, HTTPException, Query, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
import uvicorn
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.expense_analysis.services.data_service import DataService
from src.expense_analysis.agents import ExpenseClassificationAgent
from src.expense_analysis.config.performance_config import get_config
from src.expense_analysis.models.expense_models import ExpenseRecord
from src.expense_analysis.auth import verify_token_with_database
from src.expense_analysis.auth_config import AuthConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="费用分类服务API",
    description="AI费用分类服务，支持发现分类和执行分类",
    version="1.0.0"
)

# 全局变量
classification_tasks = {}  # 存储分类任务状态
task_lock = threading.Lock()


class ClassificationService:
    """分类服务类"""
    
    def __init__(self, database_name: str = "analysis_gz"):
        self.database_name = database_name
        self.data_service = DataService(database_name=database_name)
        self.agent = ExpenseClassificationAgent()
        self.config = get_config()
    
    def discover_categories(self, year_month: str = None, limit: int = 1000) -> Dict[str, Any]:
        """发现分类类别"""
        logger.info(f"开始发现分类类别，数据库: {self.database_name}, 年月: {year_month}")

        try:
            # 构建过滤条件
            filters = {}
            if year_month:
                filters['rpt_month'] = year_month

            # 使用原有的采样逻辑
            table_name = "clean_ele_payment"
            remarks = self.data_service.get_stratified_sample(table_name, filters, limit=limit)
            logger.info(f"获取到 {len(remarks)} 条备注样本")

            if not remarks:
                return {
                    "status": "success",
                    "message": "没有找到有效的备注数据",
                    "categories": [],
                    "total_records": 0,
                    "filters": filters
                }

            # 使用AI发现分类
            categories = self.agent._analyze_remarks_for_categories(remarks)

            result = {
                "status": "success",
                "message": f"成功发现 {len(categories)} 个分类类别",
                "categories": categories,
                "total_records": len(remarks),
                "database": self.database_name,
                "filters": filters,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"分类发现完成: {len(categories)} 个类别")
            return result

        except Exception as e:
            logger.error(f"分类发现失败: {e}")
            raise HTTPException(status_code=500, detail=f"分类发现失败: {str(e)}")
    
    def classify_records(self, year_month: str = None, clear_data: bool = False,
                        limit: int = None, batch_size: int = 50) -> str:
        """执行分类任务（异步）"""
        task_id = f"classify_{int(time.time())}"

        with task_lock:
            classification_tasks[task_id] = {
                "status": "running",
                "progress": 0,
                "total": 0,
                "processed": 0,
                "start_time": datetime.now().isoformat(),
                "database": self.database_name,
                "year_month": year_month,
                "clear_data": clear_data,
                "filters": {"rpt_month": year_month} if year_month else {}
            }


        # 启动后台分类任务
        def run_classification():
            try:
                self._execute_classification(task_id, year_month, clear_data, limit, batch_size)
            except Exception as e:
                logger.error(f"分类任务失败: {e}")
                with task_lock:
                    classification_tasks[task_id]["status"] = "failed"
                    classification_tasks[task_id]["error"] = str(e)

        thread = threading.Thread(target=run_classification)
        thread.daemon = True
        thread.start()

        return task_id
    
    def _execute_classification(self, task_id: str, year_month: str = None,
                               clear_data: bool = False, limit: int = None, batch_size: int = 50):
        """执行分类的内部方法 - 使用多线程和无备注预处理"""
        start_time = time.time()

        try:
            # 构建过滤条件
            filters = {}
            if year_month:
                filters['rpt_month'] = year_month

            # 如果需要清理数据，先清理指定条件的分类结果
            if clear_data:
                logger.info(f"🧹 清理数据: {filters}")

                self.data_service.clear_classification_results(filters)

            # 获取需要分类的记录
            table_name = "clean_ele_payment"

            # 获取所有符合条件的记录
            all_records = self.data_service.get_records_for_classification(table_name, filters, limit)
            total = len(all_records)

            if total == 0:
                with task_lock:
                    classification_tasks[task_id]["status"] = "completed"
                    classification_tasks[task_id]["message"] = "没有需要分类的记录"
                return

            logger.info(f"📊 开始分类任务")
            logger.info(f"   📋 总记录数: {total}")
            logger.info(f"   🧵 线程数: {self.config['max_workers']}")
            logger.info(f"   📦 批次大小: {batch_size}")
            logger.info(f"   📅 年月筛选: {year_month or '全部'}")



            # 1. 使用正确的逻辑：提取综合备注信息进行分类
            logger.info(f"🔍 提取综合备注信息...")
            comprehensive_remarks = self.data_service.get_comprehensive_remarks_for_classification(all_records)

            # 分离真正无备注和有备注的记录
            records_with_remarks = []
            records_without_remarks = []

            for i, remark_info in enumerate(comprehensive_remarks):
                record = all_records[i]
                combined_content = remark_info['combined_content'][0] if remark_info['combined_content'] else "无备注信息"

                # 判断是否真正无备注
                if combined_content == "无备注信息":
                    records_without_remarks.append(record)
                else:
                    records_with_remarks.append(record)

            logger.info(f"   📝 有备注记录: {len(records_with_remarks)} 条")
            logger.info(f"   📄 真正无备注记录: {len(records_without_remarks)} 条")

            # 2. 批量处理真正无备注的记录
            no_remark_processed = 0
            if records_without_remarks:
                logger.info(f"🔄 批量处理无备注记录...")
                for record in records_without_remarks:
                    try:
                        self.data_service.save_classification_result(record.billaccountpaymentdetail_id, "无备注", year_month)
                        no_remark_processed += 1
                    except Exception as e:
                        logger.error(f"   ❌ 保存无备注记录失败: {e}")

                logger.info(f"   ✅ 无备注记录处理完成: {no_remark_processed} 条")

            # 3. 加载或发现分类类别
            categories = self._load_or_discover_categories(records_with_remarks[:min(500, len(records_with_remarks))])
            logger.info(f"🏷️ 使用分类类别: {len(categories)} 个")
            for i, cat in enumerate(categories, 1):
                logger.info(f"   {i}. {cat}")

            # 4. 多线程处理有备注的记录，传递综合备注信息
            if records_with_remarks:
                # 只传递有备注记录对应的综合备注信息
                records_with_remarks_info = []
                for i, remark_info in enumerate(comprehensive_remarks):
                    record = all_records[i]
                    combined_content = remark_info['combined_content'][0] if remark_info['combined_content'] else "无备注信息"
                    if combined_content != "无备注信息":
                        records_with_remarks_info.append((record, remark_info))

                processed, failed = self._process_records_with_threads(
                    task_id, records_with_remarks_info, categories, batch_size, start_time, year_month
                )
            else:
                processed, failed = 0, 0

            # 5. 完成统计
            total_processed = no_remark_processed + processed
            total_time = time.time() - start_time
            success_rate = round(total_processed / total * 100, 2) if total > 0 else 0

            logger.info(f"🎉 分类任务完成!")
            logger.info(f"   📄 无备注记录: {no_remark_processed} 条")
            logger.info(f"   📝 AI分类记录: {processed} 条")
            logger.info(f"   ❌ 失败记录: {failed} 条")
            logger.info(f"   📊 总成功率: {success_rate}%")
            logger.info(f"   ⏱️ 总耗时: {total_time/60:.1f}分钟")
            logger.info(f"   ⚡ 平均速度: {total_processed/total_time:.1f} 条/秒")

            with task_lock:
                classification_tasks[task_id]["status"] = "completed"
                classification_tasks[task_id]["end_time"] = datetime.now().isoformat()
                classification_tasks[task_id]["total_time"] = total_time
                classification_tasks[task_id]["success_rate"] = success_rate
                classification_tasks[task_id]["no_remark_processed"] = no_remark_processed
                classification_tasks[task_id]["ai_processed"] = processed
                classification_tasks[task_id]["total_processed"] = total_processed
                classification_tasks[task_id]["message"] = f"成功分类 {total_processed} 条记录（无备注 {no_remark_processed} + AI分类 {processed}），失败 {failed} 条"

        except Exception as e:
            logger.error(f"❌ 分类执行失败: {e}")
            with task_lock:
                classification_tasks[task_id]["status"] = "failed"
                classification_tasks[task_id]["error"] = str(e)

    def _load_or_discover_categories(self, sample_records):
        """加载或发现分类类别"""
        # 先尝试从文件加载
        categories_file = Path("src/expense_analysis/discovered_categories.json")
        if categories_file.exists():
            try:
                with open(categories_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    categories = data.get('categories', [])
                    if categories:
                        logger.info(f"📂 从文件加载 {len(categories)} 个分类类别")
                        return categories
            except Exception as e:
                logger.warning(f"⚠️ 加载分类文件失败: {e}")

        # 如果文件不存在或加载失败，则重新发现
        logger.info("🔍 重新发现分类类别...")
        remarks_for_discovery = []
        for record in sample_records:
            if hasattr(record, 'paymentdetail_note') and record.paymentdetail_note:
                remarks_for_discovery.append(record.paymentdetail_note)
            elif hasattr(record, 'mandatory_note') and record.mandatory_note:
                remarks_for_discovery.append(record.mandatory_note)

        categories = self.agent._analyze_remarks_for_categories(remarks_for_discovery)

        # 保存到文件
        try:
            categories_data = {
                "table_name": "clean_ele_payment",
                "categories": categories,
                "created_time": datetime.now().isoformat(),
                "total_categories": len(categories)
            }
            with open(categories_file, 'w', encoding='utf-8') as f:
                json.dump(categories_data, f, ensure_ascii=False, indent=2)
            logger.info(f"💾 分类类别已保存到文件")
        except Exception as e:
            logger.warning(f"⚠️ 保存分类文件失败: {e}")

        return categories

    def _process_records_with_threads(self, task_id: str, records_with_info: List, categories: List[str],
                                    batch_size: int, start_time: float, year_month: str = None):
        """使用多线程处理有备注的记录"""
        total_records = len(records_with_info)
        processed = 0
        failed = 0

        logger.info(f"🧵 启动多线程处理 {total_records} 条有备注记录...")
        logger.info(f"   🧵 线程数: {self.config['max_workers']}")
        logger.info(f"   📦 批次大小: {batch_size}")

        # 将记录分成批次
        batches = []
        for i in range(0, total_records, batch_size):
            batch = records_with_info[i:i + batch_size]
            batches.append((i // batch_size + 1, batch))

        logger.info(f"   🔢 总批次数: {len(batches)}")

        # 使用线程池处理批次
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config['max_workers']) as executor:
            # 提交所有批次任务
            future_to_batch = {}
            for batch_num, batch in batches:
                future = executor.submit(self._process_single_batch, batch_num, batch, categories, year_month)
                future_to_batch[future] = (batch_num, len(batch))

            # 收集结果
            completed_batches = 0
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_num, batch_size = future_to_batch[future]
                completed_batches += 1

                try:
                    batch_processed, batch_failed, batch_time, actual_batch_size = future.result()
                    processed += batch_processed
                    failed += batch_failed

                    # 计算进度
                    elapsed_time = time.time() - start_time
                    progress = round(processed / total_records * 100, 2)
                    avg_time_per_record = elapsed_time / max(processed, 1)
                    remaining_records = total_records - processed
                    estimated_remaining_time = remaining_records * avg_time_per_record

                    logger.info(f"   ✅ 批次 {batch_num} 完成: 实际处理 {actual_batch_size} 条, 成功保存 {batch_processed} 条, 失败 {batch_failed} 条, 耗时 {batch_time:.2f}秒")
                    logger.info(f"   📈 总进度: {processed}/{total_records} ({progress}%) - 已完成 {completed_batches}/{len(batches)} 批次")
                    logger.info(f"   ⏳ 预计剩余: {estimated_remaining_time/60:.1f}分钟")

                    # 更新任务状态
                    with task_lock:
                        classification_tasks[task_id]["processed"] = processed
                        classification_tasks[task_id]["failed"] = failed
                        classification_tasks[task_id]["progress"] = progress
                        classification_tasks[task_id]["completed_batches"] = completed_batches
                        classification_tasks[task_id]["total_batches"] = len(batches)
                        classification_tasks[task_id]["elapsed_time"] = elapsed_time
                        classification_tasks[task_id]["estimated_remaining_time"] = estimated_remaining_time
                        classification_tasks[task_id]["avg_time_per_record"] = avg_time_per_record

                except Exception as e:
                    logger.error(f"   ❌ 批次 {batch_num} 处理失败: {e}")
                    failed += batch_size

        return processed, failed

    def _process_single_batch(self, batch_num: int, batch_records_with_info: List, categories: List[str], year_month: str = None):
        """处理单个批次的记录"""
        batch_start_time = time.time()
        batch_processed = 0
        batch_failed = 0

        # 准备批次数据
        batch_remarks = []
        batch_record_ids = []

        for record, remark_info in batch_records_with_info:
            # 使用已经处理好的综合备注内容
            combined_content = remark_info['combined_content'][0] if remark_info['combined_content'] else ""

            if combined_content and combined_content != "无备注信息":
                batch_remarks.append(combined_content)
                batch_record_ids.append(record.billaccountpaymentdetail_id)

        # AI批量分类
        if batch_remarks:
            try:
                # 传递批次信息给分类代理
                classification_results = self.agent.classify_batch_remarks_with_batch_info(
                    batch_remarks, categories, batch_num, len(batch_remarks)
                )

                # 保存分类结果
                for i, remark in enumerate(batch_remarks):
                    try:
                        if remark in classification_results:
                            result = classification_results[remark]
                            category = result.category if hasattr(result, 'category') else str(result)
                            self.data_service.save_classification_result(batch_record_ids[i], category, year_month)
                            batch_processed += 1
                        else:
                            batch_failed += 1
                    except Exception as e:
                        logger.error(f"     ❌ 保存记录 {batch_record_ids[i]} 失败: {e}")
                        batch_failed += 1

            except Exception as e:
                logger.error(f"     ❌ 批次 {batch_num} AI分类失败: {e}")
                batch_failed += len(batch_remarks)

        batch_time = time.time() - batch_start_time
        return batch_processed, batch_failed, batch_time, len(batch_records_with_info)

# API接口
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "service": "classification", "timestamp": datetime.now().isoformat()}

@app.post("/classify/discover")
async def discover_categories(
    year_month: Optional[str] = Query(None, description="年月(YYYYMM)，如202301"),
    limit: int = Query(1000, description="样本数量限制"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """发现分类类别"""
    _, database = auth_info
    service = ClassificationService(database_name=database)
    return service.discover_categories(year_month=year_month, limit=limit)

@app.post("/classify/start")
async def start_classification(
    year_month: Optional[str] = Query(None, description="年月(YYYYMM)，如202301"),
    clear_data: bool = Query(False, description="是否清理已有分类数据"),
    limit: Optional[int] = Query(None, description="分类记录数量限制"),
    batch_size: int = Query(50, description="批次大小"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """开始分类任务"""
    _, database = auth_info
    service = ClassificationService(database_name=database)
    task_id = service.classify_records(
        year_month=year_month,
        clear_data=clear_data,
        limit=limit,
        batch_size=batch_size
    )

    return {
        "status": "success",
        "task_id": task_id,
        "message": "分类任务已启动",
        "database": database,
        "year_month": year_month,
        "clear_data": clear_data
    }

@app.get("/classify/status/{task_id}")
async def get_task_status(
    task_id: str,
    auth_info: tuple = Depends(verify_token_with_database)
):
    """获取分类任务状态"""
    if task_id not in classification_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return classification_tasks[task_id]



@app.get("/classify/tasks")
async def list_tasks(
    auth_info: tuple = Depends(verify_token_with_database)
):
    """列出所有分类任务"""
    _, database = auth_info

    # 过滤当前数据库的任务
    filtered_tasks = {
        task_id: task_info
        for task_id, task_info in classification_tasks.items()
        if task_info.get("database") == database
    }

    return {
        "tasks": filtered_tasks,
        "database": database,
        "total": len(filtered_tasks)
    }

@app.get("/classify/statistics")
async def get_statistics(
    auth_info: tuple = Depends(verify_token_with_database)
):
    """获取分类统计信息"""
    _, database = auth_info
    service = ClassificationService(database_name=database)
    
    try:
        # 获取统计信息
        stats = service.data_service.get_classification_statistics()
        
        return {
            "status": "success",
            "database": database,
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

def start_server(port: int = 8003):
    """启动分类服务"""
    print("🤖 启动费用分类服务")
    print("=" * 40)
    print(f"📍 端口: {port}")
    print(f"📖 文档: http://localhost:{port}/docs")
    print("🛑 停止: Ctrl+C")
    print("=" * 40)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 分类服务已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="费用分类服务")
    parser.add_argument("action", nargs="?", choices=["start", "discover", "classify"], 
                       default="start", help="操作类型")
    parser.add_argument("--port", type=int, default=8003, help="服务端口")
    parser.add_argument("--database", default="analysis_gz", help="数据库名称")
    parser.add_argument("--limit", type=int, help="记录数量限制")
    
    args = parser.parse_args()
    
    if args.action == "start":
        start_server(args.port)
    elif args.action == "discover":
        # 命令行模式发现分类
        service = ClassificationService(args.database)
        result = service.discover_categories(limit=args.limit or 1000)
        print(f"发现结果: {result}")
    elif args.action == "classify":
        # 命令行模式执行分类
        service = ClassificationService(args.database)
        task_id = service.classify_records(limit=args.limit)
        print(f"分类任务已启动: {task_id}")

if __name__ == "__main__":
    main()
