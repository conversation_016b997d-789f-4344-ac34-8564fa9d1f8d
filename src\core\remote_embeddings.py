"""
远程Embedding模型集成
支持通过HTTP API调用远程embedding服务
"""

import requests
import logging
from typing import List, Optional
from langchain_core.embeddings import Embeddings
from src.config import settings

logger = logging.getLogger(__name__)


class RemoteEmbeddings(Embeddings):
    """
    远程Embedding模型类
    通过HTTP API调用远程embedding服务
    """
    
    def __init__(self, 
                 api_url: Optional[str] = None,
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = None,
                 timeout: int = 30):
        """
        初始化远程embedding客户端
        
        Args:
            api_url: API服务地址
            api_key: API密钥
            model_name: 模型名称
            timeout: 请求超时时间（秒）
        """
        self.api_url = api_url or settings.remote_embedding_url
        self.api_key = api_key or settings.remote_embedding_api_key
        self.model_name = model_name or settings.remote_embedding_model
        self.timeout = timeout
        
        # 验证配置
        if not self.api_url:
            raise ValueError("远程embedding服务URL未配置")
        
        logger.info(f"🌐 初始化远程Embedding服务: {self.api_url}")
        logger.info(f"📝 使用模型: {self.model_name}")
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        对文档列表进行向量化
        
        Args:
            texts: 文档文本列表
            
        Returns:
            List[List[float]]: 向量列表
        """
        try:
            logger.debug(f"🔄 开始向量化 {len(texts)} 个文档")
            
            # 构建请求数据
            request_data = {
                "input": texts,
                "model": self.model_name
            }
            
            # 构建请求头
            headers = {
                "Content-Type": "application/json"
            }
            
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            # 发送请求
            response = requests.post(
                self.api_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 提取向量数据
            if "data" in result:
                embeddings = [item["embedding"] for item in result["data"]]
                logger.debug(f"✅ 成功获取 {len(embeddings)} 个向量")
                return embeddings
            else:
                raise ValueError(f"响应格式错误: {result}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 远程embedding请求失败: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 向量化处理失败: {e}")
            raise
    
    def embed_query(self, text: str) -> List[float]:
        """
        对单个查询文本进行向量化
        
        Args:
            text: 查询文本
            
        Returns:
            List[float]: 向量
        """
        try:
            logger.debug(f"🔍 向量化查询: {text[:50]}...")
            
            # 调用文档向量化方法
            embeddings = self.embed_documents([text])
            
            if embeddings and len(embeddings) > 0:
                return embeddings[0]
            else:
                raise ValueError("向量化结果为空")
                
        except Exception as e:
            logger.error(f"❌ 查询向量化失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """
        测试与远程服务的连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info("🔧 测试远程embedding服务连接...")
            
            # 使用简单文本测试
            test_text = "测试连接"
            embedding = self.embed_query(test_text)
            
            if embedding and len(embedding) > 0:
                logger.info(f"✅ 远程embedding服务连接成功，向量维度: {len(embedding)}")
                return True
            else:
                logger.error("❌ 远程embedding服务返回空向量")
                return False
                
        except Exception as e:
            logger.error(f"❌ 远程embedding服务连接失败: {e}")
            return False


class OpenAICompatibleEmbeddings(RemoteEmbeddings):
    """
    OpenAI兼容的Embedding服务
    适用于OpenAI API或兼容OpenAI格式的服务
    """
    
    def __init__(self, 
                 api_url: Optional[str] = None,
                 api_key: Optional[str] = None,
                 model_name: Optional[str] = None):
        """
        初始化OpenAI兼容的embedding客户端
        """
        # 如果没有指定URL，使用OpenAI默认地址
        if not api_url:
            api_url = "https://api.openai.com/v1/embeddings"
        
        # 如果没有指定模型，使用默认模型
        if not model_name:
            model_name = "text-embedding-ada-002"
        
        super().__init__(api_url, api_key, model_name)


class CustomEmbeddings(RemoteEmbeddings):
    """
    自定义Embedding服务
    您可以根据自己的API格式进行定制
    """
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        自定义文档向量化实现
        请根据您的API格式修改此方法
        """
        try:
            logger.debug(f"🔄 使用自定义API向量化 {len(texts)} 个文档")
            
            # TODO: 根据您的API格式修改请求数据结构
            request_data = {
                "texts": texts,  # 可能您的API使用不同的字段名
                "model": self.model_name
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            if self.api_key:
                # TODO: 根据您的API认证方式修改
                headers["X-API-Key"] = self.api_key  # 或者其他认证方式
            
            response = requests.post(
                self.api_url,
                json=request_data,
                headers=headers,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # TODO: 根据您的API响应格式修改解析逻辑
            if "embeddings" in result:
                embeddings = result["embeddings"]
                logger.debug(f"✅ 成功获取 {len(embeddings)} 个向量")
                return embeddings
            else:
                raise ValueError(f"响应格式错误: {result}")
                
        except Exception as e:
            logger.error(f"❌ 自定义embedding请求失败: {e}")
            # 如果自定义API失败，可以回退到父类实现
            return super().embed_documents(texts)


def create_remote_embeddings() -> Embeddings:
    """
    创建远程embedding实例的工厂函数
    
    Returns:
        Embeddings: 配置好的embedding实例
    """
    if not settings.remote_embedding_enabled:
        raise ValueError("远程embedding服务未启用")
    
    # 根据URL判断使用哪种实现
    if "openai.com" in settings.remote_embedding_url:
        logger.info("🔧 使用OpenAI兼容的embedding服务")
        return OpenAICompatibleEmbeddings()
    else:
        logger.info("🔧 使用自定义embedding服务")
        # 您可以在这里选择使用CustomEmbeddings或RemoteEmbeddings
        return RemoteEmbeddings()


def test_remote_embeddings():
    """
    测试远程embedding服务
    """
    try:
        embeddings = create_remote_embeddings()
        success = embeddings.test_connection()
        
        if success:
            print("✅ 远程embedding服务测试成功")
        else:
            print("❌ 远程embedding服务测试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 远程embedding服务测试异常: {e}")
        return False


if __name__ == "__main__":
    # 直接运行此文件可以测试embedding服务
    test_remote_embeddings()
