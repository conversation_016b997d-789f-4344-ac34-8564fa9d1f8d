"""
复杂提示词管理API
提供Web界面管理向量数据库的API接口
"""

import uuid
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from src.core.prompt_vectorstore_manager import (
    get_prompt_vectorstore_manager, 
    ComplexPrompt
)

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/prompts", tags=["复杂提示词管理"])


# Pydantic模型定义
class ComplexPromptCreate(BaseModel):
    """创建复杂提示词的请求模型"""
    title: str = Field(..., description="标题")
    description: str = Field(..., description="描述")
    trigger_questions: List[str] = Field(..., description="触发问题列表")
    processing_steps: List[str] = Field(..., description="处理步骤列表")
    response_format: str = Field(..., description="回复格式要求")
    priority: int = Field(default=1, description="优先级，数字越大优先级越高")
    no_think_mode: bool = Field(default=False, description="是否启用极速模式（关闭think标签）")


class ComplexPromptUpdate(BaseModel):
    """更新复杂提示词的请求模型"""
    title: Optional[str] = Field(None, description="标题")
    description: Optional[str] = Field(None, description="描述")
    trigger_questions: Optional[List[str]] = Field(None, description="触发问题列表")
    processing_steps: Optional[List[str]] = Field(None, description="处理步骤列表")
    response_format: Optional[str] = Field(None, description="回复格式要求")
    priority: Optional[int] = Field(None, description="优先级")
    no_think_mode: Optional[bool] = Field(None, description="是否启用极速模式")


class ComplexPromptResponse(BaseModel):
    """复杂提示词响应模型"""
    id: str
    title: str
    description: str
    trigger_questions: List[str]
    processing_steps: List[str]
    response_format: str
    priority: int
    no_think_mode: bool
    created_at: str
    updated_at: str


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询")
    k: int = Field(default=5, description="返回结果数量")
    score_threshold: float = Field(default=0.7, description="相似度阈值")


class SearchResult(BaseModel):
    """搜索结果模型"""
    prompt: ComplexPromptResponse
    similarity: float


class SearchResponse(BaseModel):
    """搜索响应模型"""
    query: str
    results: List[SearchResult]
    total_count: int


# API端点
@router.post("/", response_model=ComplexPromptResponse)
async def create_prompt(prompt_data: ComplexPromptCreate):
    """创建新的复杂提示词"""
    try:
        # 生成唯一ID
        prompt_id = str(uuid.uuid4())
        
        # 创建ComplexPrompt对象
        complex_prompt = ComplexPrompt(
            id=prompt_id,
            title=prompt_data.title,
            description=prompt_data.description,
            trigger_questions=prompt_data.trigger_questions,
            processing_steps=prompt_data.processing_steps,
            response_format=prompt_data.response_format,
            priority=prompt_data.priority,
            no_think_mode=prompt_data.no_think_mode
        )
        
        # 添加到向量数据库
        manager = get_prompt_vectorstore_manager()
        success = manager.add_complex_prompt(complex_prompt)
        
        if success:
            logger.info(f"✅ 成功创建复杂提示词: {prompt_data.title}")
            return ComplexPromptResponse(**complex_prompt.__dict__)
        else:
            raise HTTPException(status_code=500, detail="创建复杂提示词失败")
            
    except Exception as e:
        logger.error(f"❌ 创建复杂提示词失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.get("/", response_model=List[ComplexPromptResponse])
async def list_prompts():
    """获取所有复杂提示词"""
    try:
        manager = get_prompt_vectorstore_manager()
        prompts = manager.get_all_prompts()
        
        # 按优先级和创建时间排序
        prompts.sort(key=lambda x: (x.priority, x.created_at), reverse=True)
        
        return [ComplexPromptResponse(**prompt.__dict__) for prompt in prompts]
        
    except Exception as e:
        logger.error(f"❌ 获取复杂提示词列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.get("/{prompt_id}", response_model=ComplexPromptResponse)
async def get_prompt(prompt_id: str):
    """根据ID获取复杂提示词"""
    try:
        manager = get_prompt_vectorstore_manager()
        prompts = manager.get_all_prompts()
        
        for prompt in prompts:
            if prompt.id == prompt_id:
                return ComplexPromptResponse(**prompt.__dict__)
        
        raise HTTPException(status_code=404, detail="复杂提示词不存在")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 获取复杂提示词失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.put("/{prompt_id}", response_model=ComplexPromptResponse)
async def update_prompt(prompt_id: str, prompt_data: ComplexPromptUpdate):
    """更新复杂提示词"""
    try:
        manager = get_prompt_vectorstore_manager()
        prompts = manager.get_all_prompts()
        
        # 查找要更新的提示词
        target_prompt = None
        for prompt in prompts:
            if prompt.id == prompt_id:
                target_prompt = prompt
                break
        
        if not target_prompt:
            raise HTTPException(status_code=404, detail="复杂提示词不存在")
        
        # 更新字段
        if prompt_data.title is not None:
            target_prompt.title = prompt_data.title
        if prompt_data.description is not None:
            target_prompt.description = prompt_data.description
        if prompt_data.trigger_questions is not None:
            target_prompt.trigger_questions = prompt_data.trigger_questions
        if prompt_data.processing_steps is not None:
            target_prompt.processing_steps = prompt_data.processing_steps
        if prompt_data.response_format is not None:
            target_prompt.response_format = prompt_data.response_format
        if prompt_data.priority is not None:
            target_prompt.priority = prompt_data.priority
        if prompt_data.no_think_mode is not None:
            target_prompt.no_think_mode = prompt_data.no_think_mode

        # 更新时间戳
        target_prompt.updated_at = datetime.now().isoformat()
        
        # 保存更新
        success = manager.update_prompt(target_prompt)
        
        if success:
            logger.info(f"✅ 成功更新复杂提示词: {target_prompt.title}")
            return ComplexPromptResponse(**target_prompt.__dict__)
        else:
            raise HTTPException(status_code=500, detail="更新复杂提示词失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 更新复杂提示词失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")


@router.delete("/{prompt_id}")
async def delete_prompt(prompt_id: str):
    """删除复杂提示词"""
    try:
        manager = get_prompt_vectorstore_manager()
        success = manager.delete_prompt(prompt_id)
        
        if success:
            logger.info(f"✅ 成功删除复杂提示词: {prompt_id}")
            return {"message": "删除成功", "id": prompt_id}
        else:
            raise HTTPException(status_code=404, detail="复杂提示词不存在或删除失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 删除复杂提示词失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.post("/search", response_model=SearchResponse)
async def search_prompts(search_request: SearchRequest):
    """搜索复杂提示词"""
    try:
        manager = get_prompt_vectorstore_manager()
        matched_prompts = manager.search_complex_prompts(
            query=search_request.query,
            k=search_request.k,
            score_threshold=search_request.score_threshold
        )
        
        results = []
        for prompt, similarity in matched_prompts:
            results.append(SearchResult(
                prompt=ComplexPromptResponse(**prompt.__dict__),
                similarity=similarity
            ))
        
        return SearchResponse(
            query=search_request.query,
            results=results,
            total_count=len(results)
        )
        
    except Exception as e:
        logger.error(f"❌ 搜索复杂提示词失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/stats/summary")
async def get_stats():
    """获取统计信息"""
    try:
        manager = get_prompt_vectorstore_manager()
        prompts = manager.get_all_prompts()
        
        # 统计信息
        total_count = len(prompts)
        priority_stats = {}
        for prompt in prompts:
            priority = prompt.priority
            if priority not in priority_stats:
                priority_stats[priority] = 0
            priority_stats[priority] += 1
        
        return {
            "total_prompts": total_count,
            "priority_distribution": priority_stats,
            "database_path": manager.persist_directory,
            "collection_name": manager.collection_name
        }
        
    except Exception as e:
        logger.error(f"❌ 获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")
