#!/usr/bin/env python3
"""
Embedding配置测试脚本
用于测试远程embedding服务配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import settings
from src.core.remote_embeddings import test_remote_embeddings

def test_embedding_configuration():
    """测试embedding配置"""
    print("🧪 测试Embedding配置")
    print("=" * 50)
    
    # 检查配置
    print("📋 当前配置:")
    print(f"  远程embedding启用: {settings.remote_embedding_enabled}")
    print(f"  服务URL: {settings.remote_embedding_url}")
    print(f"  API密钥: {'已配置' if settings.remote_embedding_api_key else '未配置'}")
    print(f"  模型名称: {settings.remote_embedding_model}")
    
    if not settings.remote_embedding_enabled:
        print("\n⚠️ 远程embedding服务未启用")
        print("💡 请在.env文件中配置:")
        print("   REMOTE_EMBEDDING_ENABLED=true")
        print("   REMOTE_EMBEDDING_URL=您的embedding服务地址")
        print("   REMOTE_EMBEDDING_API_KEY=您的API密钥")
        print("   REMOTE_EMBEDDING_MODEL=您的模型名称")
        return False
    
    # 测试连接
    print("\n🔧 测试远程embedding服务连接...")
    success = test_remote_embeddings()
    
    return success

def show_configuration_examples():
    """显示配置示例"""
    print("\n📝 配置示例")
    print("=" * 50)
    
    print("1. OpenAI API配置:")
    print("   REMOTE_EMBEDDING_ENABLED=true")
    print("   REMOTE_EMBEDDING_URL=https://api.openai.com/v1/embeddings")
    print("   REMOTE_EMBEDDING_API_KEY=sk-your-openai-api-key")
    print("   REMOTE_EMBEDDING_MODEL=text-embedding-ada-002")
    
    print("\n2. 自定义API配置:")
    print("   REMOTE_EMBEDDING_ENABLED=true")
    print("   REMOTE_EMBEDDING_URL=http://your-server:8001/embeddings")
    print("   REMOTE_EMBEDDING_API_KEY=your-custom-api-key")
    print("   REMOTE_EMBEDDING_MODEL=your-model-name")
    
    print("\n3. 本地服务配置:")
    print("   REMOTE_EMBEDDING_ENABLED=true")
    print("   REMOTE_EMBEDDING_URL=http://localhost:8001/embeddings")
    print("   REMOTE_EMBEDDING_API_KEY=")
    print("   REMOTE_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2")

def test_vector_store_initialization():
    """测试向量存储初始化"""
    print("\n🧪 测试向量存储初始化")
    print("=" * 50)
    
    try:
        from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager
        
        print("🔄 初始化向量存储管理器...")
        manager = get_prompt_vectorstore_manager()
        
        print("✅ 向量存储管理器初始化成功")
        print(f"📁 存储目录: {manager.persist_directory}")
        print(f"📦 集合名称: {manager.collection_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量存储初始化失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Embedding配置测试")
    print("=" * 60)
    
    # 测试配置
    config_ok = test_embedding_configuration()
    
    if config_ok:
        # 测试向量存储初始化
        vector_ok = test_vector_store_initialization()
        
        if vector_ok:
            print("\n✅ 所有测试通过！")
            print("🎉 您的embedding配置正确，可以开始使用向量增强功能")
        else:
            print("\n❌ 向量存储初始化失败")
    else:
        print("\n❌ Embedding配置测试失败")
        show_configuration_examples()
    
    print("\n" + "=" * 60)
    print("📋 下一步:")
    if config_ok:
        print("1. 创建示例复杂提示词:")
        print("   python examples/sample_complex_prompts.py")
        print("2. 启动API服务器:")
        print("   python examples/analysis_agent_server.py")
        print("3. 访问管理界面:")
        print("   http://localhost:8000/admin")
    else:
        print("1. 配置您的远程embedding服务")
        print("2. 重新运行此测试脚本")

if __name__ == "__main__":
    main()
