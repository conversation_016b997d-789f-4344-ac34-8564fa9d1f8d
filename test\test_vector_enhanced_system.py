#!/usr/bin/env python3
"""
向量增强系统完整测试
"""

import sys
import os
import json
import requests
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_prompt_management_api():
    """测试复杂提示词管理API"""
    print("🧪 测试复杂提示词管理API")
    print("=" * 50)
    
    base_url = "http://localhost:8000/api/prompts"
    
    # 测试获取所有提示词
    try:
        print("📋 测试获取提示词列表...")
        response = requests.get(base_url)
        if response.status_code == 200:
            prompts = response.json()
            print(f"✅ 成功获取 {len(prompts)} 个提示词")
            for prompt in prompts[:3]:  # 只显示前3个
                print(f"  - {prompt['title']} (优先级: {prompt['priority']})")
        else:
            print(f"❌ 获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试搜索功能
    try:
        print("\n🔍 测试搜索功能...")
        search_data = {
            "query": "电费情况如何",
            "k": 3,
            "score_threshold": 0.7
        }
        response = requests.post(f"{base_url}/search", json=search_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 搜索成功，找到 {result['total_count']} 个匹配结果")
            for i, item in enumerate(result['results'], 1):
                print(f"  [{i}] {item['prompt']['title']} (相似度: {item['similarity']:.3f})")
        else:
            print(f"❌ 搜索失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 搜索请求失败: {e}")
    
    # 测试统计信息
    try:
        print("\n📊 测试统计信息...")
        response = requests.get(f"{base_url}/stats/summary")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 统计信息获取成功")
            print(f"  总提示词数: {stats['total_prompts']}")
            print(f"  优先级分布: {stats['priority_distribution']}")
        else:
            print(f"❌ 统计信息获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 统计请求失败: {e}")

def test_vector_enhanced_chat():
    """测试向量增强的聊天功能"""
    print("\n🧪 测试向量增强聊天功能")
    print("=" * 50)
    
    api_url = "http://localhost:8000/v1/chat/completions"
    
    # 测试用例
    test_cases = [
        {
            "name": "电费情况分析",
            "message": "当前电费情况如何",
            "expected_complex_prompt": True
        },
        {
            "name": "铁塔服务费查询",
            "message": "2025年4月铁塔服务费是多少",
            "expected_complex_prompt": True
        },
        {
            "name": "普通查询",
            "message": "今天天气怎么样",
            "expected_complex_prompt": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"问题: {test_case['message']}")
        
        request_body = {
            "model": "analysis_agent",
            "messages": [
                {"role": "system", "content": "数据库:GZ"},
                {"role": "user", "content": test_case['message']}
            ],
            "stream": False,
            "show_think_tags": False
        }
        
        try:
            start_time = time.time()
            response = requests.post(api_url, json=request_body, timeout=60)
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                print(f"✅ 响应成功 (用时: {duration:.2f}秒)")
                
                # 检查是否使用了复杂提示词
                if 'used_complex_prompt' in result:
                    used_complex = result['used_complex_prompt']
                    if used_complex:
                        prompt_info = result.get('complex_prompt_info', {})
                        print(f"🎯 使用了复杂提示词: {prompt_info.get('title', 'Unknown')}")
                        print(f"   相似度: {prompt_info.get('similarity', 0):.3f}")
                    else:
                        print("🔄 使用了默认处理逻辑")
                    
                    # 验证预期
                    if used_complex == test_case['expected_complex_prompt']:
                        print("✅ 符合预期")
                    else:
                        print("⚠️ 与预期不符")
                
                # 显示响应摘要
                lines = content.split('\n')
                summary = '\n'.join(lines[:5])
                print(f"📄 响应摘要: {summary}...")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
        
        time.sleep(1)  # 避免请求过快

def test_web_interface():
    """测试Web管理界面"""
    print("\n🧪 测试Web管理界面")
    print("=" * 50)
    
    try:
        # 测试管理页面
        response = requests.get("http://localhost:8000/admin")
        if response.status_code == 200:
            print("✅ 管理界面可访问")
            print("🌐 访问地址: http://localhost:8000/admin")
        else:
            print(f"❌ 管理界面访问失败: {response.status_code}")
        
        # 测试静态文件
        response = requests.get("http://localhost:8000/static/js/prompt_management.js")
        if response.status_code == 200:
            print("✅ 静态文件服务正常")
        else:
            print(f"❌ 静态文件访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Web界面测试失败: {e}")

def test_system_integration():
    """测试系统集成"""
    print("\n🧪 测试系统集成")
    print("=" * 50)
    
    # 测试完整流程：创建提示词 -> 搜索 -> 聊天
    base_url = "http://localhost:8000/api/prompts"
    
    # 1. 创建测试提示词
    test_prompt = {
        "title": "测试提示词",
        "description": "用于测试的示例提示词",
        "trigger_keywords": ["测试", "示例"],
        "processing_steps": ["步骤1：分析问题", "步骤2：提供答案"],
        "response_format": "简单的测试回复格式",
        "priority": 1
    }
    
    try:
        print("1. 创建测试提示词...")
        response = requests.post(base_url, json=test_prompt)
        if response.status_code == 200:
            created_prompt = response.json()
            prompt_id = created_prompt['id']
            print(f"✅ 创建成功，ID: {prompt_id}")
            
            # 2. 搜索测试
            print("2. 搜索测试提示词...")
            search_data = {"query": "测试", "k": 1, "score_threshold": 0.5}
            response = requests.post(f"{base_url}/search", json=search_data)
            if response.status_code == 200:
                search_result = response.json()
                if search_result['total_count'] > 0:
                    print("✅ 搜索成功")
                else:
                    print("⚠️ 搜索无结果")
            
            # 3. 删除测试提示词
            print("3. 清理测试数据...")
            response = requests.delete(f"{base_url}/{prompt_id}")
            if response.status_code == 200:
                print("✅ 清理成功")
            else:
                print("⚠️ 清理失败")
                
        else:
            print(f"❌ 创建失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")

def main():
    """主函数"""
    print("🚀 向量增强系统完整测试")
    print("=" * 60)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ API服务器未运行，请先启动:")
            print("   python examples/analysis_agent_server.py")
            return
    except:
        print("❌ 无法连接到API服务器，请先启动:")
        print("   python examples/analysis_agent_server.py")
        return
    
    print("✅ API服务器运行正常\n")
    
    # 运行各项测试
    test_prompt_management_api()
    test_vector_enhanced_chat()
    test_web_interface()
    test_system_integration()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("1. 复杂提示词管理API - 测试完成")
    print("2. 向量增强聊天功能 - 测试完成")
    print("3. Web管理界面 - 测试完成")
    print("4. 系统集成 - 测试完成")
    print("\n💡 使用指南:")
    print("- 管理界面: http://localhost:8000/admin")
    print("- API文档: http://localhost:8000/docs")
    print("- 创建示例数据: python examples/sample_complex_prompts.py")

if __name__ == "__main__":
    main()
