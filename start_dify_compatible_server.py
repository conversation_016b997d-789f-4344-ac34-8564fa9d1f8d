#!/usr/bin/env python3
"""
快速启动 Dify 兼容的 API 服务器
"""

import sys
import os
import subprocess

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 启动 Dify 兼容的 API 服务器")
    print("=" * 40)
    
    # 检查环境变量文件
    env_file = ".env"
    if not os.path.exists(env_file):
        print("❌ 未找到 .env 配置文件")
        print("\n请创建 .env 文件，包含以下配置：")
        print("""
# 本地LLM配置
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_api_key_here
LOCAL_LLM_MODEL_URL=http://localhost:9997/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B

# API服务器配置
API_SERVER_HOST=0.0.0.0
API_SERVER_PORT=8000

# 可选：BGE嵌入模型配置
LOCAL_BGE_ENABLED=false
LOCAL_BGE_API_URL=http://localhost:9997/v1/embeddings
LOCAL_BGE_MODEL_NAME=bge-large-zh-v1.5
        """)
        return
    
    print("✅ 找到配置文件，启动服务器...\n")
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = os.getcwd()
    
    try:
        # 启动服务器
        subprocess.run([
            sys.executable, 
            "examples/openai_compatible_server.py"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
    except FileNotFoundError:
        print("❌ 找不到服务器文件，请确保文件路径正确")

if __name__ == "__main__":
    main() 