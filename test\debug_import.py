#!/usr/bin/env python3
"""
调试导入问题
检查 OpenAI 兼容服务器的依赖是否正常
"""

import sys
import traceback

def test_imports():
    """测试所有必要的导入"""
    print("🔍 开始检查依赖导入...")
    
    imports_to_test = [
        ("fastapi", "FastAPI"),
        ("fastapi.middleware.cors", "CORSMiddleware"), 
        ("fastapi.responses", "StreamingResponse"),
        ("pydantic", "BaseModel"),
        ("uvicorn", None),
        ("langchain", None),
        ("langchain_openai", None),
        ("numpy", None),
        ("requests", None),
    ]
    
    failed_imports = []
    
    for module_name, class_name in imports_to_test:
        try:
            if class_name:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                print(f"✅ {module_name}.{class_name}")
            else:
                __import__(module_name)
                print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            failed_imports.append((module_name, str(e)))
        except Exception as e:
            print(f"⚠️ {module_name}: {e}")
            failed_imports.append((module_name, str(e)))
    
    return failed_imports

def test_project_imports():
    """测试项目内部模块导入"""
    print("\n🔍 检查项目模块导入...")
    
    # 添加项目路径
    import os
    project_root = os.path.dirname(os.path.abspath(__file__))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    project_imports = [
        "src.core",
        "src.config", 
    ]
    
    failed_imports = []
    
    for module_name in project_imports:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            failed_imports.append((module_name, str(e)))
        except Exception as e:
            print(f"⚠️ {module_name}: {e}")
            failed_imports.append((module_name, str(e)))
    
    return failed_imports

def test_config_access():
    """测试配置访问"""
    print("\n🔍 检查配置访问...")
    
    try:
        from src.config import settings
        print(f"✅ 配置加载成功")
        print(f"   LOCAL_LLM_ENABLED: {getattr(settings, 'local_llm_enabled', 'N/A')}")
        print(f"   LOCAL_LLM_MODEL_URL: {getattr(settings, 'local_llm_model_url', 'N/A')}")
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        traceback.print_exc()
        return False

def test_llm_factory():
    """测试LLM工厂"""
    print("\n🔍 检查LLM工厂...")
    
    try:
        from src.core import LLMFactory
        print(f"✅ LLMFactory 导入成功")
        return True
    except Exception as e:
        print(f"❌ LLMFactory 导入失败: {e}")
        traceback.print_exc()
        return False

def test_server_startup():
    """测试服务器启动脚本"""
    print("\n🔍 测试服务器启动脚本...")
    
    try:
        # 模拟导入服务器脚本
        import os
        server_path = os.path.join("examples", "openai_compatible_server.py")
        
        if not os.path.exists(server_path):
            print(f"❌ 服务器文件不存在: {server_path}")
            return False
        
        # 尝试编译文件（不执行）
        with open(server_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        compile(source, server_path, 'exec')
        print(f"✅ 服务器脚本语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ 服务器脚本语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 服务器脚本检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🐛 OpenAI 兼容服务器依赖调试")
    print("=" * 60)
    
    # 1. 测试基础依赖
    failed_basic = test_imports()
    
    # 2. 测试项目模块
    failed_project = test_project_imports()
    
    # 3. 测试配置
    config_ok = test_config_access()
    
    # 4. 测试LLM工厂
    factory_ok = test_llm_factory()
    
    # 5. 测试服务器脚本
    server_ok = test_server_startup()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 调试总结:")
    
    if failed_basic:
        print(f"❌ 基础依赖问题 ({len(failed_basic)} 个):")
        for module, error in failed_basic:
            print(f"   - {module}: {error}")
    else:
        print("✅ 基础依赖正常")
    
    if failed_project:
        print(f"❌ 项目模块问题 ({len(failed_project)} 个):")
        for module, error in failed_project:
            print(f"   - {module}: {error}")
    else:
        print("✅ 项目模块正常")
    
    print(f"{'✅' if config_ok else '❌'} 配置访问{'正常' if config_ok else '异常'}")
    print(f"{'✅' if factory_ok else '❌'} LLM工厂{'正常' if factory_ok else '异常'}")
    print(f"{'✅' if server_ok else '❌'} 服务器脚本{'正常' if server_ok else '异常'}")
    
    # 建议
    print("\n🔧 建议修复步骤:")
    if failed_basic or failed_project or not config_ok or not factory_ok:
        print("1. 运行重装脚本: python reinstall_all.py")
        print("2. 检查环境变量配置")
        print("3. 确认项目结构完整")
    else:
        print("所有依赖正常，问题可能在其他地方")
    
    return len(failed_basic) == 0 and len(failed_project) == 0 and config_ok and factory_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 调试过程中发生错误: {e}")
        traceback.print_exc()
        sys.exit(1) 