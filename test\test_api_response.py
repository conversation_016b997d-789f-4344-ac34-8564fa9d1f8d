#!/usr/bin/env python3
"""
测试API响应数据结构
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.prompt_vectorstore_manager import get_prompt_vectorstore_manager

def test_api_response():
    """测试API响应数据结构"""
    print("🧪 测试API响应数据结构")
    print("=" * 50)
    
    try:
        manager = get_prompt_vectorstore_manager()
        
        # 获取所有提示词
        prompts = manager.get_all_prompts()
        print(f"📋 获取到 {len(prompts)} 个提示词")
        
        if prompts:
            # 检查第一个提示词的结构
            first_prompt = prompts[0]
            print(f"\n📝 第一个提示词结构:")
            print(f"ID: {first_prompt.id}")
            print(f"标题: {first_prompt.title}")
            print(f"描述: {first_prompt.description}")
            print(f"触发问题: {first_prompt.trigger_questions}")
            print(f"处理步骤: {first_prompt.processing_steps}")
            print(f"优先级: {first_prompt.priority}")
            
            # 转换为字典格式（模拟API响应）
            prompt_dict = {
                "id": first_prompt.id,
                "title": first_prompt.title,
                "description": first_prompt.description,
                "trigger_questions": first_prompt.trigger_questions,
                "processing_steps": first_prompt.processing_steps,
                "response_format": first_prompt.response_format,
                "priority": first_prompt.priority,
                "created_at": first_prompt.created_at,
                "updated_at": first_prompt.updated_at
            }
            
            print(f"\n📊 API响应格式:")
            print(json.dumps(prompt_dict, ensure_ascii=False, indent=2))
            
            # 检查是否有trigger_keywords字段
            if hasattr(first_prompt, 'trigger_keywords'):
                print(f"\n⚠️ 发现旧字段 trigger_keywords: {first_prompt.trigger_keywords}")
            else:
                print(f"\n✅ 没有旧字段 trigger_keywords")
                
        else:
            print("❌ 没有找到提示词数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_javascript_compatibility():
    """测试JavaScript兼容性"""
    print("\n" + "=" * 50)
    print("🧪 测试JavaScript兼容性")
    
    # 模拟前端可能收到的数据
    mock_prompt = {
        "id": "test-id",
        "title": "测试提示词",
        "description": "测试描述",
        "trigger_questions": ["问题1", "问题2", "问题3"],
        "processing_steps": ["步骤1", "步骤2"],
        "response_format": "格式",
        "priority": 1,
        "created_at": "2024-01-01",
        "updated_at": "2024-01-01"
    }
    
    print("📊 模拟前端数据:")
    print(json.dumps(mock_prompt, ensure_ascii=False, indent=2))
    
    # 检查前端需要的字段
    required_fields = ["trigger_questions"]
    missing_fields = []
    
    for field in required_fields:
        if field not in mock_prompt:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 缺少字段: {missing_fields}")
    else:
        print("✅ 所有必需字段都存在")
        
        # 测试map操作
        try:
            questions = mock_prompt["trigger_questions"]
            if isinstance(questions, list):
                print(f"✅ trigger_questions是数组，长度: {len(questions)}")
                print(f"✅ 可以执行map操作")
            else:
                print(f"❌ trigger_questions不是数组: {type(questions)}")
        except Exception as e:
            print(f"❌ map操作失败: {e}")

if __name__ == "__main__":
    test_api_response()
    test_javascript_compatibility()
