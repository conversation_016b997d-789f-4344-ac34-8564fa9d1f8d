"""
知识库查询工具
用于查询业务逻辑、指标计算等知识库信息
"""

import json
import requests
import logging
import time
from typing import Dict, Any, List
from pydantic import BaseModel, Field

from .base_tools import BaseTool, ToolInput, ToolOutput
from src.config import settings

# 设置日志记录器
logger = logging.getLogger(__name__)


class KnowledgeSearchInput(ToolInput):
    """知识库查询工具输入"""
    query: str = Field(description="要查询的问题或关键词")


class KnowledgeSearchTool(BaseTool):
    """知识库查询工具 - 查询业务逻辑和指标计算相关信息"""
    
    def __init__(self, 
                 api_url: str = None,
                 api_key: str = None):
        """
        初始化知识库查询工具
        
        Args:
            api_url: 知识库查询API地址（可选，默认使用环境变量）
            api_key: API密钥（可选，默认使用环境变量）
        """
        super().__init__(
            name="knowledge_search",
            description="查询业务逻辑、指标计算方法、概念定义、操作流程等知识信息。适用于询问'如何计算'、'什么是'、'为什么'等概念性问题，不涉及具体数据查询。"
        )
        self.api_url = api_url or settings.knowledge_search_api_url
        self.api_key = api_key or settings.knowledge_search_api_key
    
    def call(self, query: str) -> ToolOutput:
        """
        执行知识库查询
        
        Args:
            query: 查询问题或关键词
            
        Returns:
            包含查询结果的输出
        """
        total_start_time = time.time()
        
        try:
            search_msg = f"🔍 正在查询知识库: {query}"
            print(search_msg)
            logger.info(f"📚 [知识库] {search_msg}")
            logger.info(f"📚 [知识库] API地址: {self.api_url}")
            
            # 构造请求头
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # 构造请求体
            payload = {
                "inputs": {"query": query},
                "response_mode": "blocking",
                "user": "sql-agent"
            }
            
            logger.info(f"🌐 [知识库] 发送API请求...")
            logger.info(f"📤 [知识库] 请求体: {payload}")
            
            # 发送请求
            request_start = time.time()
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            request_duration = time.time() - request_start
            
            logger.info(f"🌐 [知识库] API响应状态码: {response.status_code} (请求用时: {request_duration:.2f}秒)")
            
            if response.status_code == 200:
                parse_start = time.time()
                result = response.json()
                logger.info(f"📥 [知识库] API返回数据结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")
                
                # 解析返回结果
                knowledge_results = self._parse_knowledge_results(result)
                parse_duration = time.time() - parse_start
                
                total_duration = time.time() - total_start_time
                
                if knowledge_results:
                    # 格式化结果
                    format_start = time.time()
                    formatted_result = self._format_knowledge_results(query, knowledge_results)
                    format_duration = time.time() - format_start
                    
                    success_msg = f"✅ 知识库查询成功，找到 {len(knowledge_results)} 条相关信息 (总用时: {total_duration:.2f}秒，请求: {request_duration:.2f}秒，解析: {parse_duration:.3f}秒，格式化: {format_duration:.3f}秒)"
                    print(success_msg)
                    logger.info(f"📚 [知识库] {success_msg}")
                    
                    # 记录查询结果详情
                    for i, result_item in enumerate(knowledge_results[:3], 1):  # 记录前3条
                        logger.info(f"📄 [知识库结果{i}] 标题: {result_item.get('title', 'N/A')}")
                        logger.info(f"📄 [知识库结果{i}] 相关度: {result_item.get('score', 'N/A')}")
                        content_preview = result_item.get('content', '')[:100] + '...' if len(result_item.get('content', '')) > 100 else result_item.get('content', '')
                        logger.info(f"📄 [知识库结果{i}] 内容预览: {content_preview}")
                    
                    return ToolOutput(
                        success=True,
                        result={
                            "query": query,
                            "results": knowledge_results,
                            "formatted_answer": formatted_result,
                            "result_count": len(knowledge_results),
                            "timing": {
                                "request": request_duration,
                                "parse": parse_duration,
                                "format": format_duration,
                                "total": total_duration
                            }
                        },
                        error=None
                    )
                else:
                    error_msg = f"未在知识库中找到相关信息 (总用时: {total_duration:.2f}秒)"
                    logger.warning(f"⚠️ [知识库] {error_msg}")
                    return ToolOutput(
                        success=False,
                        result=None,
                        error=error_msg
                    )
            else:
                total_duration = time.time() - total_start_time
                error_msg = f"知识库API请求失败，状态码: {response.status_code}，响应: {response.text} (总用时: {total_duration:.2f}秒)"
                logger.error(f"❌ [知识库] {error_msg}")
                return ToolOutput(
                    success=False,
                    result=None,
                    error=error_msg
                )
                
        except requests.exceptions.RequestException as e:
            total_duration = time.time() - total_start_time
            error_msg = f"网络请求失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"🌐 [知识库] {error_msg}")
            return ToolOutput(
                success=False,
                result=None,
                error=error_msg
            )
        except Exception as e:
            total_duration = time.time() - total_start_time
            error_msg = f"知识库查询失败: {str(e)} (总用时: {total_duration:.2f}秒)"
            logger.error(f"💥 [知识库] {error_msg}")
            return ToolOutput(
                success=False,
                result=None,
                error=error_msg
            )
    
    def _parse_knowledge_results(self, api_response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析知识库API返回结果"""
        try:
            # 从API响应中提取results
            data = api_response.get("data", {})
            outputs = data.get("outputs", {})
            results = outputs.get("result", [])
            
            if not results or not isinstance(results, list):
                return []
            
            # 提取有用信息
            parsed_results = []
            for item in results:
                if isinstance(item, dict) and "content" in item:
                    parsed_item = {
                        "title": item.get("title", "未知文档"),
                        "content": item.get("content", ""),
                        "score": item.get("metadata", {}).get("score", 0),
                        "dataset_name": item.get("metadata", {}).get("dataset_name", ""),
                        "document_name": item.get("metadata", {}).get("document_name", "")
                    }
                    parsed_results.append(parsed_item)
            
            # 按相关性分数排序
            parsed_results.sort(key=lambda x: x.get("score", 0), reverse=True)
            
            return parsed_results
            
        except Exception as e:
            print(f"❌ 解析知识库结果失败: {e}")
            return []
    
    def _format_knowledge_results(self, query: str, results: List[Dict[str, Any]]) -> str:
        """格式化知识库查询结果"""
        if not results:
            return f"关于「{query}」的问题，未在知识库中找到相关信息。"
        
        formatted = f"**关于「{query}」的知识库查询结果：**\n\n"
        
        for i, result in enumerate(results[:5], 1):  # 最多显示5条结果
            title = result.get("title", "未知文档")
            content = result.get("content", "")
            score = result.get("score", 0)
            dataset_name = result.get("dataset_name", "")
            
            formatted += f"**📖 结果 {i}** (相关度: {score:.2f})\n"
            formatted += f"**来源:** {title}\n"
            if dataset_name:
                formatted += f"**数据集:** {dataset_name}\n"
            formatted += f"**内容:** {content}\n\n"
            formatted += "---\n\n"
        
        if len(results) > 5:
            formatted += f"*注：共找到 {len(results)} 条结果，仅显示前5条最相关的信息*\n\n"
        
        formatted += "*💡 如需更多详细信息，请尝试更具体的关键词查询*"
        
        return formatted
    
    def get_schema(self) -> Dict[str, Any]:
        """获取工具模式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "要查询的问题或关键词，例如：'用电成本计算逻辑'、'直供电电费算法'、'指标计算方法'"
                        }
                    },
                    "required": ["query"]
                }
            }
        }
    
    def get_input_schema(self) -> Dict[str, Any]:
        """获取输入参数schema"""
        return KnowledgeSearchInput.model_json_schema()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.get_input_schema()
        } 