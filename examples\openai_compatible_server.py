"""
OpenAI 兼容的 API 服务器
将您的本地模型封装为兼容 OpenAI API 格式的服务，方便在 Dify 等平台中接入
"""

import sys
import os
import time
import uuid
import json
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from src.core import LLMFactory
from src.config import settings


# ===== Pydantic 模型定义 =====

class ChatMessage(BaseModel):
    role: str = Field(..., description="消息角色: user, assistant, system")
    content: str = Field(..., description="消息内容")


class ChatCompletionRequest(BaseModel):
    model: str = Field(..., description="模型名称")
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, description="生成温度", ge=0.0, le=2.0)
    top_p: Optional[float] = Field(1.0, description="nucleus采样参数", ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(0.0, description="频率惩罚", ge=-2.0, le=2.0)
    presence_penalty: Optional[float] = Field(0.0, description="存在惩罚", ge=-2.0, le=2.0)
    stream: Optional[bool] = Field(False, description="是否流式输出")
    stop: Optional[List[str]] = Field(None, description="停止词列表")


class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: str


class ChatCompletionUsage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: ChatCompletionUsage


class ChatCompletionStreamChoice(BaseModel):
    index: int
    delta: Dict[str, Any]
    finish_reason: Optional[str] = None


class ChatCompletionStreamResponse(BaseModel):
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[ChatCompletionStreamChoice]


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str


class ModelsResponse(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


class EmbeddingRequest(BaseModel):
    model: str = Field(..., description="嵌入模型名称")
    input: List[str] = Field(..., description="要嵌入的文本列表")


class EmbeddingData(BaseModel):
    object: str = "embedding"
    embedding: List[float]
    index: int


class EmbeddingUsage(BaseModel):
    prompt_tokens: int
    total_tokens: int


class EmbeddingResponse(BaseModel):
    object: str = "list"
    data: List[EmbeddingData]
    model: str
    usage: EmbeddingUsage


# ===== FastAPI 应用设置 =====

app = FastAPI(
    title="OpenAI 兼容 API 服务器",
    description="将本地模型封装为 OpenAI API 兼容格式",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量存储模型实例
_llm_model = None
_embedding_model = None


# ===== 工具函数 =====

def get_current_timestamp() -> int:
    """获取当前时间戳"""
    return int(time.time())


def generate_completion_id() -> str:
    """生成聊天完成的唯一ID"""
    return f"chatcmpl-{uuid.uuid4().hex[:24]}"


def estimate_tokens(text: str) -> int:
    """估算token数量（简单实现，1个中文字符约等于1个token）"""
    # 这是一个简化的token估算，实际应该使用具体的tokenizer
    chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
    english_words = len(text.split()) - chinese_chars
    return chinese_chars + english_words


def init_models():
    """初始化模型"""
    global _llm_model, _embedding_model
    
    try:
        print("🚀 正在初始化模型...")
        
        # 初始化语言模型
        _llm_model = LLMFactory.create_chat_model(
            provider="local",
            temperature=0.7
        )
        print("✅ 语言模型初始化成功")
        
        # 初始化嵌入模型（可选）
        try:
            if settings.local_bge_enabled:
                _embedding_model = LLMFactory.create_embeddings(provider="local_bge")
                print("✅ 嵌入模型初始化成功")
            else:
                print("⚠️ 嵌入模型未启用")
        except Exception as e:
            print(f"⚠️ 嵌入模型初始化失败: {e}")
            _embedding_model = None
            
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        raise e


def format_messages_for_prompt(messages: List[ChatMessage]) -> str:
    """将消息列表格式化为模型输入的prompt"""
    formatted_parts = []
    
    for message in messages:
        role = message.role
        content = message.content
        
        if role == "system":
            formatted_parts.append(f"System: {content}")
        elif role == "user":
            formatted_parts.append(f"Human: {content}")
        elif role == "assistant":
            formatted_parts.append(f"Assistant: {content}")
    
    # 添加最后的Assistant前缀，让模型知道该它回复了
    formatted_parts.append("Assistant:")
    
    return "\n\n".join(formatted_parts)


# ===== API 路由 =====

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "OpenAI 兼容 API 服务器",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "models": "/v1/models",
            "chat_completions": "/v1/chat/completions",
            "embeddings": "/v1/embeddings"
        }
    }


@app.get("/v1/models")
async def list_models():
    """列出可用模型"""
    models = []
    
    # 添加聊天模型
    if _llm_model:
        models.append(ModelInfo(
            id=settings.local_llm_model_name or "local-chat-model",
            created=get_current_timestamp(),
            owned_by="local"
        ))
    
    # 添加嵌入模型
    if _embedding_model:
        models.append(ModelInfo(
            id=settings.local_bge_model_name or "local-embedding-model",
            created=get_current_timestamp(),
            owned_by="local"
        ))
    
    return ModelsResponse(data=models)


@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """创建聊天完成"""
    print(f"🚀 DEBUG: [openai_compatible_server] /v1/chat/completions 端点被调用")
    print(f"  - 请求体: {request.model_dump()}")
    
    if not _llm_model:
        print(f"❌ DEBUG: 语言模型未初始化")
        raise HTTPException(status_code=503, detail="语言模型未初始化")
    
    try:
        # 格式化消息为prompt
        prompt = format_messages_for_prompt(request.messages)
        
        if request.stream:
            # 流式响应
            return StreamingResponse(
                stream_chat_completion(request, prompt),
                media_type="text/plain"
            )
        else:
            # 非流式响应
            return await create_chat_completion_response(request, prompt)
            
    except Exception as e:
        print(f"❌ 聊天完成处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


async def create_chat_completion_response(request: ChatCompletionRequest, prompt: str) -> ChatCompletionResponse:
    """创建非流式聊天完成响应"""
    
    print(f"🔍 DEBUG: [openai_compatible_server] 收到聊天请求")
    print(f"  - 模型: {request.model}")
    print(f"  - 消息数量: {len(request.messages)}")
    print(f"  - 最后一条消息: {request.messages[-1].content if request.messages else 'None'}")
    print(f"  - 格式化的prompt: '{prompt}'")
    
    # 调用模型生成响应
    response_text = _llm_model.invoke(prompt)
    print(f"  - LLM原始响应: '{response_text}'")
    
    # 估算token使用量
    prompt_tokens = estimate_tokens(prompt)
    completion_tokens = estimate_tokens(response_text)
    total_tokens = prompt_tokens + completion_tokens
    
    print(f"  - Token统计: prompt={prompt_tokens}, completion={completion_tokens}, total={total_tokens}")
    
    # 构建响应
    completion_id = generate_completion_id()
    timestamp = get_current_timestamp()
    
    response = ChatCompletionResponse(
        id=completion_id,
        created=timestamp,
        model=request.model,
        choices=[
            ChatCompletionChoice(
                index=0,
                message=ChatMessage(role="assistant", content=response_text),
                finish_reason="stop"
            )
        ],
        usage=ChatCompletionUsage(
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens
        )
    )
    
    print(f"🔍 DEBUG: [openai_compatible_server] 构建的响应:")
    print(f"  - ID: {completion_id}")
    print(f"  - 响应内容: '{response_text}'")
    print(f"  - 响应对象类型: {type(response)}")
    print(f"  - 响应JSON预览: {response.model_dump_json()[:200]}...")
    
    return response


async def stream_chat_completion(request: ChatCompletionRequest, prompt: str) -> AsyncGenerator[str, None]:
    """流式聊天完成生成器"""
    completion_id = generate_completion_id()
    created = get_current_timestamp()
    
    try:
        # 由于我们的本地模型不支持流式，这里模拟流式输出
        response_text = _llm_model.invoke(prompt)
        
        # 将响应分块发送
        words = response_text.split()
        for i, word in enumerate(words):
            if i == 0:
                # 第一个chunk
                chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0,
                            delta={"role": "assistant", "content": word + " "}
                        )
                    ]
                )
            elif i == len(words) - 1:
                # 最后一个chunk
                chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0,
                            delta={"content": word},
                            finish_reason="stop"
                        )
                    ]
                )
            else:
                # 中间的chunk
                chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0,
                            delta={"content": word + " "}
                        )
                    ]
                )
            
            yield f"data: {chunk.model_dump_json()}\n\n"
            await asyncio.sleep(0.05)  # 模拟延迟
        
        # 发送结束标记
        yield "data: [DONE]\n\n"
        
    except Exception as e:
        error_chunk = {
            "error": {
                "message": str(e),
                "type": "server_error"
            }
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"


@app.post("/v1/embeddings")
async def create_embeddings(request: EmbeddingRequest):
    """创建嵌入向量"""
    if not _embedding_model:
        raise HTTPException(status_code=503, detail="嵌入模型未初始化")
    
    try:
        # 获取嵌入向量
        embeddings = _embedding_model.embed_documents(request.input)
        
        # 构建响应数据
        data = []
        for i, embedding in enumerate(embeddings):
            data.append(EmbeddingData(
                embedding=embedding,
                index=i
            ))
        
        # 估算token使用量
        total_tokens = sum(estimate_tokens(text) for text in request.input)
        
        return EmbeddingResponse(
            data=data,
            model=request.model,
            usage=EmbeddingUsage(
                prompt_tokens=total_tokens,
                total_tokens=total_tokens
            )
        )
        
    except Exception as e:
        print(f"❌ 嵌入处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"嵌入处理失败: {str(e)}")


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "llm_model": _llm_model is not None,
        "embedding_model": _embedding_model is not None,
        "timestamp": datetime.now().isoformat()
    }


# ===== 启动事件 =====

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    init_models()


# ===== 主函数 =====

def main():
    """主函数"""
    print("=== OpenAI 兼容 API 服务器 ===\n")
    
    # 检查配置
    if not settings.local_llm_enabled:
        print("❌ 本地LLM未启用")
        print("请在.env文件中设置: LOCAL_LLM_ENABLED=true")
        return
    
    if not settings.local_llm_model_url:
        print("❌ 未设置本地LLM模型URL")
        return
    
    # 配置信息
    host = os.getenv("API_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("API_SERVER_PORT", "8000"))
    
    print(f"配置信息:")
    print(f"  服务地址: http://{host}:{port}")
    print(f"  模型URL: {settings.local_llm_model_url}")
    print(f"  模型名称: {settings.local_llm_model_name}")
    print(f"  嵌入模型启用: {settings.local_bge_enabled}")
    print()
    
    print("API 端点:")
    print(f"  模型列表: http://{host}:{port}/v1/models")
    print(f"  聊天完成: http://{host}:{port}/v1/chat/completions")
    print(f"  嵌入向量: http://{host}:{port}/v1/embeddings")
    print(f"  健康检查: http://{host}:{port}/health")
    print()
    
    print("在 Dify 中的配置:")
    print(f"  API 端点: http://{host}:{port}/v1")
    print(f"  模型名称: {settings.local_llm_model_name or 'local-chat-model'}")
    print(f"  API 密钥: 任意值（本地服务不验证）")
    print()
    
    print("启动服务器...")
    
    # 启动服务器
    uvicorn.run(
        "openai_compatible_server:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )


if __name__ == "__main__":
    main() 