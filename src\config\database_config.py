"""
数据库配置文件
用于配置SQL执行工具的数据库连接信息
"""

import os
from typing import Dict, Any


class DatabaseConfig:
    """数据库配置类"""
    
    # MySQL数据库配置
    MYSQL_CONFIG = {
        "host": os.getenv("MYSQL_HOST", "localhost"),           # 数据库服务器地址
        "port": int(os.getenv("MYSQL_PORT", 3306)),            # 数据库端口
        "user": os.getenv("MYSQL_USER", "root"),               # 数据库用户名
        "password": os.getenv("MYSQL_PASSWORD", "password"),   # 数据库密码
        "database": os.getenv("MYSQL_DATABASE", "test"),       # 数据库名称
        "charset": os.getenv("MYSQL_CHARSET", "utf8mb4")       # 字符编码
    }
    
    # PostgreSQL数据库配置（备用）
    POSTGRESQL_CONFIG = {
        "host": os.getenv("POSTGRESQL_HOST", "localhost"),
        "port": int(os.getenv("POSTGRESQL_PORT", 5432)),
        "user": os.getenv("POSTGRESQL_USER", "postgres"),
        "password": os.getenv("POSTGRESQL_PASSWORD", "password"),
        "database": os.getenv("POSTGRESQL_DATABASE", "test")
    }
    
    @classmethod
    def get_mysql_config(cls, database_name: str = None) -> Dict[str, Any]:
        """
        获取MySQL配置

        Args:
            database_name: 指定的数据库名称，如果为None则使用默认配置

        Returns:
            Dict[str, Any]: MySQL配置字典
        """
        config = cls.MYSQL_CONFIG.copy()
        if database_name:
            config["database"] = database_name
        return config
    
    @classmethod
    def get_postgresql_config(cls) -> Dict[str, Any]:
        """获取PostgreSQL配置"""
        return cls.POSTGRESQL_CONFIG.copy()
    
    @classmethod
    def update_mysql_config(cls, **kwargs) -> None:
        """更新MySQL配置"""
        cls.MYSQL_CONFIG.update(kwargs)
    
    @classmethod
    def set_mysql_config(cls, host: str, port: int, user: str, password: str, database: str, charset: str = "utf8mb4"):
        """直接设置MySQL配置"""
        cls.MYSQL_CONFIG = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database,
            "charset": charset
        }


# 默认配置示例
DEFAULT_MYSQL_CONFIG = {
    "host": "***********",     # 您的MySQL服务器IP
    "port": 3306,              # MySQL端口
    "user": "your_username",   # 您的用户名
    "password": "your_password", # 您的密码
    "database": "your_database", # 您的数据库名
    "charset": "utf8mb4"
} 