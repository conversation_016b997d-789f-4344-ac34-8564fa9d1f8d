"""
性能优化配置
简化配置，固定4线程
"""

# 固定配置
CONCURRENT_CONFIG = {
    # 线程池配置
    "max_workers": 4,  # 固定4线程
    "batch_size": 50,  # 每批次处理50条记录

    # AI调用优化
    "ai_timeout": 300,  # AI调用超时时间(秒)
    "retry_attempts": 2,  # 失败重试次数

    # 数据库优化
    "db_batch_commit": True,  # 批量提交
    "connection_pool_size": 8,  # 连接池大小

    # 性能监控
    "enable_progress_tracking": True,  # 启用进度跟踪
    "log_batch_performance": True,  # 记录批次性能
}

def get_config():
    """获取配置"""
    return CONCURRENT_CONFIG.copy()

if __name__ == "__main__":
    config = get_config()
    print("🔧 费用分析性能配置")
    print("=" * 40)
    print(f"🧵 线程数: {config['max_workers']}")
    print(f"📦 批次大小: {config['batch_size']}")
    print(f"⏱️ 超时时间: {config['ai_timeout']}秒")
