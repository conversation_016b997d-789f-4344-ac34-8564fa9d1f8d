2025-08-14 11:11:32,596 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-14 11:11:32,600 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-14 11:11:32,600 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-14 11:11:32,601 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-14 11:11:32,603 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-14 11:11:32,603 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-14 11:11:32,604 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-14 11:11:32,605 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-14 11:11:33,199 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-14 11:11:34,391 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-14 11:11:34,829 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-14 11:11:34,831 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-14 11:11:34,831 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-14 11:12:22,107 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-14 11:12:22,110 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-14 11:12:22,111 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-14 11:12:22,111 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-14 11:12:22,113 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-14 11:12:22,115 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-14 11:12:22,115 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-14 11:12:22,116 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-14 11:12:22,418 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-14 11:12:23,156 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-14 11:12:23,341 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-14 11:12:23,344 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-14 11:12:23,344 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
2025-08-14 16:25:14,261 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-14 16:25:14,276 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-14 16:25:14,276 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-14 16:25:14,277 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-14 16:25:14,278 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-14 16:25:14,279 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-14 16:25:14,279 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-14 16:25:14,280 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-14 16:25:14,669 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-14 16:25:15,543 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-14 16:25:15,855 - chromadb.telemetry.product.posthog - ERROR - Failed to send telemetry event ClientStartEvent: capture() takes 1 positional argument but 3 were given
2025-08-14 16:25:15,858 - src.core.prompt_vectorstore_manager - ERROR - ❌ 向量数据库初始化失败: '_type'
2025-08-14 16:25:15,860 - __main__ - ERROR - ❌ SQL Agent初始化失败: '_type'
